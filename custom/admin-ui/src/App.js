import React, { useState, useEffect } from 'react';
import { Users, UserPlus, Cloud, Trash2, KeyRound, X, Plus, Loader2, AlertTriangle } from 'lucide-react';
import './index.css'; // Tailwind base

const API_BASE_URL = window.RUNTIME_CONFIG?.API_BASE_URL || 
                     process.env.REACT_APP_API_BASE_URL || 
                     'http://localhost:8084';

function App() {
  const [customers, setCustomers] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const [showAddCredentials, setShowAddCredentials] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedCloud, setSelectedCloud] = useState('');
  const [credentialSchemas, setCredentialSchemas] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [useTestEndpoints, setUseTestEndpoints] = useState(true);
  const [darkMode, setDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('darkMode');
      return saved ? JSON.parse(saved) : false;
    }
    return false;
  });

  useEffect(() => {
    fetchCustomers();
    fetchCredentialSchemas();
  }, []);

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    localStorage.setItem('darkMode', JSON.stringify(darkMode));
  }, [darkMode]);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      const endpoint = useTestEndpoints ? '/api/admin/test/customers' : '/api/admin/customers';
      const response = await fetch(`${API_BASE_URL}${endpoint}`);
      const data = await response.json();
      if (data.status === 'success') {
        setCustomers(data.customers);
        // Auto-select the first customer if none is selected
        if (data.customers.length > 0 && !selectedCustomer) {
          setSelectedCustomer(data.customers[0]);
        }
      } else {
        setError(data.error);
      }
    } catch (err) {
      setError('Failed to fetch customers: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchCredentialSchemas = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/admin/credential-schemas`);
      const data = await response.json();
      if (data.status === 'success') {
        setCredentialSchemas(data.schemas);
      }
    } catch (err) {
      // ignore
    }
  };

  const handleAddCustomer = async (customerData) => {
    try {
      setLoading(true);
      const endpoint = useTestEndpoints ? '/api/admin/test/customers' : '/api/admin/customers';
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customerData),
      });
      const data = await response.json();
      if (data.status === 'success') {
        await fetchCustomers();
        setShowAddCustomer(false);
      } else {
        setError(data.error);
      }
    } catch (err) {
      setError('Failed to add customer: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCustomer = async (customerId) => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/admin/customers/${customerId}`, { method: 'DELETE' });
      const data = await response.json();
      if (data.status === 'success') {
        await fetchCustomers();
        setSelectedCustomer(null);
        setShowDeleteConfirm(false);
      } else {
        setError(data.error);
      }
    } catch (err) {
      setError('Failed to delete customer');
    } finally {
      setLoading(false);
    }
  };

  const handleAddCredentials = async (credentialData) => {
    if (!selectedCustomer || !selectedCloud) return;
    try {
      setLoading(true);
      const endpoint = useTestEndpoints 
        ? `/api/admin/test/customers/${selectedCustomer.id}/credentials/${selectedCloud}`
        : `/api/admin/customers/${selectedCustomer.id}/credentials/${selectedCloud}`;
      const response = await fetch(
        `${API_BASE_URL}${endpoint}`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(credentialData),
        }
      );
      const data = await response.json();
      if (data.status === 'success') {
        await fetchCustomers();
        setShowAddCredentials(false);
        setSelectedCloud('');
      } else {
        setError(data.error);
      }
    } catch (err) {
      setError('Failed to add credentials');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCredentials = async (customerId, cloud) => {
    if (!window.confirm(`Are you sure you want to delete ${cloud} credentials?`)) return;
    try {
      setLoading(true);
      const response = await fetch(
        `${API_BASE_URL}/api/admin/customers/${customerId}/credentials/${cloud}`,
        { method: 'DELETE' }
      );
      const data = await response.json();
      if (data.status === 'success') {
        await fetchCustomers();
      } else {
        setError(data.error);
      }
    } catch (err) {
      setError('Failed to delete credentials');
    } finally {
      setLoading(false);
    }
  };

  // Also, if the customer list changes (e.g. after add/delete), auto-select the first if none is selected
  useEffect(() => {
    if (customers.length > 0 && !selectedCustomer) {
      setSelectedCustomer(customers[0]);
    }
  }, [customers, selectedCustomer]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 transition-colors duration-200">
      <header className="bg-blue-700 text-white px-8 py-4 flex justify-between items-center shadow">
        <h1 className="text-2xl font-semibold flex items-center gap-2">
          <Users className="w-7 h-7" />
          Steampipe Compliance Admin
        </h1>
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2 cursor-pointer text-sm">
            <input
              type="checkbox"
              checked={useTestEndpoints}
              onChange={(e) => {
                setUseTestEndpoints(e.target.checked);
                fetchCustomers();
              }}
              className="accent-blue-600"
            />
            Use Test Endpoints (No Auth)
          </label>
          <button
            onClick={() => setDarkMode((d) => !d)}
            className="p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
            aria-label={darkMode ? 'Switch to light mode' : 'Switch to dark mode'}
          >
            {darkMode ? (
              <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 3v1m0 16v1m8.66-13.66l-.71.71M4.05 19.07l-.71.71M21 12h-1M4 12H3m16.66 5.66l-.71-.71M4.05 4.93l-.71-.71M12 5a7 7 0 100 14 7 7 0 000-14z" /></svg>
            ) : (
              <svg className="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M21 12.79A9 9 0 1111.21 3a7 7 0 109.79 9.79z" /></svg>
            )}
          </button>
        </div>
      </header>

      <main className="max-w-5xl mx-auto py-8 px-4 flex flex-col min-h-[80vh] justify-center items-center">
        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-500 rounded-md flex items-start gap-3 animate-fadeIn">
            <X className="w-5 h-5 mt-0.5 flex-shrink-0 cursor-pointer" onClick={() => setError(null)} />
            <div>
              <p className="font-medium">{error}</p>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-4xl items-start">
          {customers.length === 0 ? (
            // Empty state when no customers
            <div className="col-span-1 md:col-span-2 flex justify-center">
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-12 transition-all duration-300 hover:shadow-3xl text-center max-w-md">
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-6">
                  <Users className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">No Customers Found</h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  Get started by creating your first customer to manage their cloud compliance credentials.
                </p>
                <button
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-full shadow-lg hover:from-indigo-600 hover:to-blue-600 transition mx-auto"
                  onClick={() => setShowAddCustomer(true)}
                  disabled={loading}
                >
                  <UserPlus className="w-5 h-5" /> Add Your First Customer
                </button>
              </div>
            </div>
          ) : (
            <>
              {/* Customers List */}
              <section className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-8 transition-all duration-300 hover:shadow-3xl flex flex-col">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
                    <Users className="w-5 h-5 text-blue-600" /> Customers
                  </h2>
                  <button
                    className="flex items-center gap-2 px-5 py-2 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-full shadow-lg hover:from-indigo-600 hover:to-blue-600 transition"
                    onClick={() => setShowAddCustomer(true)}
                    disabled={loading}
                  >
                    <UserPlus className="w-4 h-4" /> Add Customer
                  </button>
                </div>
                {loading && <div className="flex items-center gap-2 text-gray-500"><Loader2 className="w-4 h-4 animate-spin" /> Loading...</div>}
                <div className="flex flex-col gap-3 max-h-65 overflow-y-auto">
                  {customers.map((customer) => (
                    <div
                      key={customer.id}
                      className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${selectedCustomer?.id === customer.id ? 'border-blue-600 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700'}`}
                      onClick={() => setSelectedCustomer(customer)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{customer.name}</h3>
                          <p className="text-xs text-gray-500 dark:text-gray-400">ID: {customer.id}</p>
                        </div>
                        <div className="flex gap-2">
                          {['aws', 'azure', 'gcp'].map((cloud) => (
                            <span
                              key={cloud}
                              className={`px-2 py-1 rounded-full text-xs font-semibold ${customer.cloud_credentials[cloud]?.configured ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'}`}
                            >
                              {cloud.toUpperCase()}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </section>

              {/* Customer Details */}
              {selectedCustomer && (
                <section className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-8 transition-all duration-300 hover:shadow-3xl">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
                      <UserPlus className="w-5 h-5 text-indigo-600" /> {selectedCustomer.name}
                    </h2>
                    <button
                      className="flex items-center gap-2 px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-all duration-200 shadow"
                      onClick={() => setShowDeleteConfirm(true)}
                    >
                      <Trash2 className="w-4 h-4" /> Delete Customer
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-4">ID: {selectedCustomer.id}</p>
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                      <KeyRound className="w-5 h-5 text-indigo-600" /> Cloud Credentials
                    </h3>
                    {['aws', 'azure', 'gcp'].map((cloud) => (
                      <div key={cloud} className="flex items-center justify-between border rounded-lg p-3 bg-gray-50 dark:bg-gray-700">
                        <div className="flex items-center gap-2">
                          <Cloud className="w-4 h-4 text-blue-500" />
                          <span className="font-medium text-gray-900 dark:text-white">{cloud.toUpperCase()}</span>
                          <span className={`ml-2 text-xs font-semibold ${selectedCustomer.cloud_credentials[cloud]?.configured ? 'text-green-600' : 'text-red-600'}`}>
                            {selectedCustomer.cloud_credentials[cloud]?.configured ? '✓ Configured' : '✗ Not configured'}
                          </span>
                        </div>
                        {selectedCustomer.cloud_credentials[cloud]?.configured ? (
                          <button
                            className="flex items-center gap-1 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 text-xs"
                            onClick={() => handleDeleteCredentials(selectedCustomer.id, cloud)}
                          >
                            <Trash2 className="w-3 h-3" /> Delete
                          </button>
                        ) : (
                          <button
                            className="flex items-center gap-1 px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 text-xs"
                            onClick={() => { setSelectedCloud(cloud); setShowAddCredentials(true); }}
                          >
                            <Plus className="w-3 h-3" /> Add
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                </section>
              )}
            </>
          )}
        </div>

        {/* Add Customer Modal */}
        {showAddCustomer && (
          <Modal onClose={() => setShowAddCustomer(false)}>
            <h2 className="text-xl font-bold mb-4 flex items-center gap-2 text-gray-900 dark:text-white"><UserPlus className="w-5 h-5 text-indigo-600" /> Add New Customer</h2>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                handleAddCustomer({
                  name: formData.get('name'),
                  id: formData.get('id') || undefined,
                });
              }}
              className="space-y-4"
            >
              <div>
                <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200">Customer Name*</label>
                <input type="text" name="name" required className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white text-gray-900 dark:bg-gray-900 dark:text-white dark:border-gray-700" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200">Customer ID (optional)</label>
                <input type="text" name="id" placeholder="Auto-generated if empty" className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white text-gray-900 dark:bg-gray-900 dark:text-white dark:border-gray-700" />
              </div>
              <div className="flex justify-end gap-2 mt-6">
                <button type="button" onClick={() => setShowAddCustomer(false)} className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700">Cancel</button>
                <button type="submit" className="px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700">Add Customer</button>
              </div>
            </form>
          </Modal>
        )}

        {/* Add Credentials Modal */}
        {showAddCredentials && selectedCloud && (
          <Modal onClose={() => setShowAddCredentials(false)} large>
            <h2 className="text-xl font-bold mb-4 flex items-center gap-2"><KeyRound className="w-5 h-5 text-indigo-600" /> Add {selectedCloud.toUpperCase()} Credentials</h2>
            <CredentialForm
              cloud={selectedCloud}
              schema={credentialSchemas[selectedCloud]}
              onSubmit={handleAddCredentials}
              onCancel={() => setShowAddCredentials(false)}
            />
          </Modal>
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && selectedCustomer && (
          <Modal onClose={() => setShowDeleteConfirm(false)}>
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 mb-4">
                <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">Delete Customer</h2>
              <div className="text-left mb-6">
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Are you sure you want to delete <span className="font-semibold text-red-600 dark:text-red-400">{selectedCustomer.name}</span>?
                </p>
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg p-4">
                  <h4 className="font-semibold text-red-800 dark:text-red-300 mb-2">This action will permanently delete:</h4>
                  <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                    <li>• Customer: <span className="font-medium">{selectedCustomer.name}</span></li>
                    <li>• Customer ID: <span className="font-medium">{selectedCustomer.id}</span></li>
                    {Object.entries(selectedCustomer.cloud_credentials || {}).map(([cloud, cred]) => 
                      cred.configured && (
                        <li key={cloud}>• {cloud.toUpperCase()} credentials</li>
                      )
                    )}
                    {!Object.values(selectedCustomer.cloud_credentials || {}).some(cred => cred.configured) && (
                      <li>• No cloud credentials configured</li>
                    )}
                  </ul>
                </div>
                <p className="text-red-600 dark:text-red-400 text-sm mt-3 font-medium">
                  ⚠️ This action cannot be undone.
                </p>
              </div>
              <div className="flex justify-end gap-3">
                <button 
                  type="button" 
                  onClick={() => setShowDeleteConfirm(false)} 
                  className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700 transition-colors"
                >
                  Cancel
                </button>
                <button 
                  onClick={() => handleDeleteCustomer(selectedCustomer.id)}
                  disabled={loading}
                  className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {loading ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4" />
                      Delete Customer
                    </>
                  )}
                </button>
              </div>
            </div>
          </Modal>
        )}
      </main>
    </div>
  );
}

function Modal({ children, onClose, large }) {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-full ${large ? 'w-[32rem]' : 'w-[24rem]'} relative animate-fadeIn`}>
        <button className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" onClick={onClose}>
          <X className="w-5 h-5" />
        </button>
        {children}
      </div>
    </div>
  );
}

function CredentialForm({ cloud, schema, onSubmit, onCancel }) {
  const [formData, setFormData] = useState({});
  const [fileFeedback, setFileFeedback] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
  };

  const renderField = (field, required) => {
    if (cloud === 'gcp' && field === 'private_key') {
      return (
        <div key={field} className="mb-4">
          <label className="block text-sm font-medium mb-1">{field.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}{required && '*'}</label>
          <textarea
            name={field}
            required={required}
            rows={8}
            placeholder="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            onChange={(e) => handleChange(field, e.target.value)}
          />
        </div>
      );
    }
    return (
      <div key={field} className="mb-4">
        <label className="block text-sm font-medium mb-1">{field.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}{required && '*'}</label>
        <input
          type={field.includes('secret') || field.includes('password') ? 'password' : 'text'}
          name={field}
          required={required}
          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          onChange={(e) => handleChange(field, e.target.value)}
        />
      </div>
    );
  };

  if (cloud === 'gcp') {
    const handleFileUpload = (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
          try {
            const json = JSON.parse(event.target.result);
            setFormData(json);
            setFileFeedback(`File loaded: ${file.name}`);
          } catch (err) {
            setFileFeedback('Invalid JSON file');
          }
        };
        reader.readAsText(file);
      }
    };
    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded text-sm text-blue-700 dark:text-blue-200 mb-2">Upload your GCP service account JSON key file.</div>
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">Service Account JSON File</label>
          <input
            type="file"
            accept=".json"
            required
            onChange={handleFileUpload}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          <div className="mt-2 text-xs text-green-600 dark:text-green-300">{fileFeedback}</div>
        </div>
        <div className="flex justify-end gap-2 mt-6">
          <button type="button" onClick={onCancel} className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700">Cancel</button>
          <button type="submit" className="px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700">Save Credentials</button>
        </div>
      </form>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {schema?.required?.map((field) => renderField(field, true))}
      {schema?.optional?.map((field) => renderField(field, false))}
      <div className="flex justify-end gap-2 mt-6">
        <button type="button" onClick={onCancel} className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700">Cancel</button>
        <button type="submit" className="px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700">Save Credentials</button>
      </div>
    </form>
  );
}

export default App;