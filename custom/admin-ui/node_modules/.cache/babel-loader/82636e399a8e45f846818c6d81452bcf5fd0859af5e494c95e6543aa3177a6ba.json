{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/steampipe-compliance-unified/custom/admin-ui/src/App.js\",\n  _window$RUNTIME_CONFI,\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Users, UserPlus, Cloud, Trash2, KeyRound, X, Plus, Loader2, AlertTriangle } from 'lucide-react';\nimport './index.css'; // Tailwind base\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = ((_window$RUNTIME_CONFI = window.RUNTIME_CONFIG) === null || _window$RUNTIME_CONFI === void 0 ? void 0 : _window$RUNTIME_CONFI.API_BASE_URL) || process.env.REACT_APP_API_BASE_URL || 'http://localhost:8084';\nfunction App() {\n  _s();\n  const [customers, setCustomers] = useState([]);\n  const [selectedCustomer, setSelectedCustomer] = useState(null);\n  const [showAddCustomer, setShowAddCustomer] = useState(false);\n  const [showAddCredentials, setShowAddCredentials] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [selectedCloud, setSelectedCloud] = useState('');\n  const [credentialSchemas, setCredentialSchemas] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [useTestEndpoints, setUseTestEndpoints] = useState(true);\n  const [darkMode, setDarkMode] = useState(() => {\n    if (typeof window !== 'undefined') {\n      const saved = localStorage.getItem('darkMode');\n      return saved ? JSON.parse(saved) : false;\n    }\n    return false;\n  });\n  useEffect(() => {\n    fetchCustomers();\n    fetchCredentialSchemas();\n  }, []);\n  useEffect(() => {\n    if (darkMode) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n    localStorage.setItem('darkMode', JSON.stringify(darkMode));\n  }, [darkMode]);\n  const fetchCustomers = async () => {\n    try {\n      setLoading(true);\n      const endpoint = useTestEndpoints ? '/api/admin/test/customers' : '/api/admin/customers';\n      const response = await fetch(`${API_BASE_URL}${endpoint}`);\n      const data = await response.json();\n      if (data.status === 'success') {\n        setCustomers(data.customers);\n        // Auto-select the first customer if none is selected\n        if (data.customers.length > 0 && !selectedCustomer) {\n          setSelectedCustomer(data.customers[0]);\n        }\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to fetch customers: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchCredentialSchemas = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/api/admin/credential-schemas`);\n      const data = await response.json();\n      if (data.status === 'success') {\n        setCredentialSchemas(data.schemas);\n      }\n    } catch (err) {\n      // ignore\n    }\n  };\n  const handleAddCustomer = async customerData => {\n    try {\n      setLoading(true);\n      const endpoint = useTestEndpoints ? '/api/admin/test/customers' : '/api/admin/customers';\n      const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(customerData)\n      });\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n        setShowAddCustomer(false);\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to add customer: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteCustomer = async customerId => {\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/admin/customers/${customerId}`, {\n        method: 'DELETE'\n      });\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n        setSelectedCustomer(null);\n        setShowDeleteConfirm(false);\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to delete customer');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAddCredentials = async credentialData => {\n    if (!selectedCustomer || !selectedCloud) return;\n    try {\n      setLoading(true);\n      const endpoint = useTestEndpoints ? `/api/admin/test/customers/${selectedCustomer.id}/credentials/${selectedCloud}` : `/api/admin/customers/${selectedCustomer.id}/credentials/${selectedCloud}`;\n      const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(credentialData)\n      });\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n        setShowAddCredentials(false);\n        setSelectedCloud('');\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to add credentials');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteCredentials = async (customerId, cloud) => {\n    if (!window.confirm(`Are you sure you want to delete ${cloud} credentials?`)) return;\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/admin/customers/${customerId}/credentials/${cloud}`, {\n        method: 'DELETE'\n      });\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to delete credentials');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Also, if the customer list changes (e.g. after add/delete), auto-select the first if none is selected\n  useEffect(() => {\n    if (customers.length > 0 && !selectedCustomer) {\n      setSelectedCustomer(customers[0]);\n    }\n  }, [customers, selectedCustomer]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 transition-colors duration-200\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-blue-700 text-white px-8 py-4 flex justify-between items-center shadow\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-semibold flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Users, {\n          className: \"w-7 h-7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), \"Steampipe Compliance Admin\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center gap-2 cursor-pointer text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: useTestEndpoints,\n            onChange: e => {\n              setUseTestEndpoints(e.target.checked);\n              fetchCustomers();\n            },\n            className: \"accent-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), \"Use Test Endpoints (No Auth)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setDarkMode(d => !d),\n          className: \"p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200\",\n          \"aria-label\": darkMode ? 'Switch to light mode' : 'Switch to dark mode',\n          children: darkMode ? /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 text-yellow-500\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: 2,\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M12 3v1m0 16v1m8.66-13.66l-.71.71M4.05 19.07l-.71.71M21 12h-1M4 12H3m16.66 5.66l-.71-.71M4.05 4.93l-.71-.71M12 5a7 7 0 100 14 7 7 0 000-14z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 126\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 text-gray-700\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: 2,\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M21 12.79A9 9 0 1111.21 3a7 7 0 109.79 9.79z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 124\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"max-w-5xl mx-auto py-8 px-4 flex flex-col min-h-[80vh] justify-center items-center\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-500 rounded-md flex items-start gap-3 animate-fadeIn\",\n        children: [/*#__PURE__*/_jsxDEV(X, {\n          className: \"w-5 h-5 mt-0.5 flex-shrink-0 cursor-pointer\",\n          onClick: () => setError(null)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-medium\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-4xl items-start\",\n        children: customers.length === 0 ?\n        /*#__PURE__*/\n        // Empty state when no customers\n        _jsxDEV(\"div\", {\n          className: \"col-span-1 md:col-span-2 flex justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-12 transition-all duration-300 hover:shadow-3xl text-center max-w-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-6\",\n              children: /*#__PURE__*/_jsxDEV(Users, {\n                className: \"h-8 w-8 text-blue-600 dark:text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 dark:text-white mb-3\",\n              children: \"No Customers Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500 dark:text-gray-400 mb-6\",\n              children: \"Get started by creating your first customer to manage their cloud compliance credentials.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-full shadow-lg hover:from-indigo-600 hover:to-blue-600 transition mx-auto\",\n              onClick: () => setShowAddCustomer(true),\n              disabled: loading,\n              children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this), \" Add Your First Customer\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"section\", {\n            className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-8 transition-all duration-300 hover:shadow-3xl flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(Users, {\n                  className: \"w-5 h-5 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this), \" Customers\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"flex items-center gap-2 px-5 py-2 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-full shadow-lg hover:from-indigo-600 hover:to-blue-600 transition\",\n                onClick: () => setShowAddCustomer(true),\n                disabled: loading,\n                children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this), \" Add Customer\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 text-gray-500\",\n              children: [/*#__PURE__*/_jsxDEV(Loader2, {\n                className: \"w-4 h-4 animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 84\n              }, this), \" Loading...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col gap-3 max-h-65 overflow-y-auto\",\n              children: customers.map(customer => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `border rounded-lg p-4 cursor-pointer transition-all duration-200 ${(selectedCustomer === null || selectedCustomer === void 0 ? void 0 : selectedCustomer.id) === customer.id ? 'border-blue-600 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700'}`,\n                onClick: () => setSelectedCustomer(customer),\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                      children: customer.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500 dark:text-gray-400\",\n                      children: [\"ID: \", customer.id]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex gap-2\",\n                    children: ['aws', 'azure', 'gcp'].map(cloud => {\n                      var _customer$cloud_crede;\n                      return /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-2 py-1 rounded-full text-xs font-semibold ${(_customer$cloud_crede = customer.cloud_credentials[cloud]) !== null && _customer$cloud_crede !== void 0 && _customer$cloud_crede.configured ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'}`,\n                        children: cloud.toUpperCase()\n                      }, cloud, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 272,\n                        columnNumber: 29\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 23\n                }, this)\n              }, customer.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this), selectedCustomer && /*#__PURE__*/_jsxDEV(\"section\", {\n            className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-8 transition-all duration-300 hover:shadow-3xl\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n                  className: \"w-5 h-5 text-indigo-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this), \" \", selectedCustomer.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"flex items-center gap-2 px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-all duration-200 shadow\",\n                onClick: () => setShowDeleteConfirm(true),\n                children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 23\n                }, this), \" Delete Customer\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 dark:text-gray-400 mb-4\",\n              children: [\"ID: \", selectedCustomer.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(KeyRound, {\n                  className: \"w-5 h-5 text-indigo-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 23\n                }, this), \" Cloud Credentials\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 21\n              }, this), ['aws', 'azure', 'gcp'].map(cloud => {\n                var _selectedCustomer$clo, _selectedCustomer$clo2, _selectedCustomer$clo3;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between border rounded-lg p-3 bg-gray-50 dark:bg-gray-700\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Cloud, {\n                      className: \"w-4 h-4 text-blue-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-900 dark:text-white\",\n                      children: cloud.toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 309,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `ml-2 text-xs font-semibold ${(_selectedCustomer$clo = selectedCustomer.cloud_credentials[cloud]) !== null && _selectedCustomer$clo !== void 0 && _selectedCustomer$clo.configured ? 'text-green-600' : 'text-red-600'}`,\n                      children: (_selectedCustomer$clo2 = selectedCustomer.cloud_credentials[cloud]) !== null && _selectedCustomer$clo2 !== void 0 && _selectedCustomer$clo2.configured ? '✓ Configured' : '✗ Not configured'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 25\n                  }, this), (_selectedCustomer$clo3 = selectedCustomer.cloud_credentials[cloud]) !== null && _selectedCustomer$clo3 !== void 0 && _selectedCustomer$clo3.configured ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"flex items-center gap-1 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 text-xs\",\n                    onClick: () => handleDeleteCredentials(selectedCustomer.id, cloud),\n                    children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                      className: \"w-3 h-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 319,\n                      columnNumber: 29\n                    }, this), \" Delete\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"flex items-center gap-1 px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 text-xs\",\n                    onClick: () => {\n                      setSelectedCloud(cloud);\n                      setShowAddCredentials(true);\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Plus, {\n                      className: \"w-3 h-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 29\n                    }, this), \" Add\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 27\n                  }, this)]\n                }, cloud, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 23\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), showAddCustomer && /*#__PURE__*/_jsxDEV(Modal, {\n        onClose: () => setShowAddCustomer(false),\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-4 flex items-center gap-2 text-gray-900 dark:text-white\",\n          children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n            className: \"w-5 h-5 text-indigo-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 106\n          }, this), \" Add New Customer\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: e => {\n            e.preventDefault();\n            const formData = new FormData(e.target);\n            handleAddCustomer({\n              name: formData.get('name'),\n              id: formData.get('id') || undefined\n            });\n          },\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200\",\n              children: \"Customer Name*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"name\",\n              required: true,\n              className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white text-gray-900 dark:bg-gray-900 dark:text-white dark:border-gray-700\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200\",\n              children: \"Customer ID (optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"id\",\n              placeholder: \"Auto-generated if empty\",\n              className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white text-gray-900 dark:bg-gray-900 dark:text-white dark:border-gray-700\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end gap-2 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowAddCustomer(false),\n              className: \"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700\",\n              children: \"Add Customer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 11\n      }, this), showAddCredentials && selectedCloud && /*#__PURE__*/_jsxDEV(Modal, {\n        onClose: () => setShowAddCredentials(false),\n        large: true,\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-4 flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(KeyRound, {\n            className: \"w-5 h-5 text-indigo-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 76\n          }, this), \" Add \", selectedCloud.toUpperCase(), \" Credentials\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CredentialForm, {\n          cloud: selectedCloud,\n          schema: credentialSchemas[selectedCloud],\n          onSubmit: handleAddCredentials,\n          onCancel: () => setShowAddCredentials(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 11\n      }, this), showDeleteConfirm && selectedCustomer && /*#__PURE__*/_jsxDEV(Modal, {\n        onClose: () => setShowDeleteConfirm(false),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n              className: \"h-6 w-6 text-red-600 dark:text-red-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold mb-4 text-gray-900 dark:text-white\",\n            children: \"Delete Customer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-left mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 dark:text-gray-300 mb-4\",\n              children: [\"Are you sure you want to delete \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-red-600 dark:text-red-400\",\n                children: selectedCustomer.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 51\n              }, this), \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold text-red-800 dark:text-red-300 mb-2\",\n                children: \"This action will permanently delete:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"text-sm text-red-700 dark:text-red-300 space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [\"\\u2022 Customer: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: selectedCustomer.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 37\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [\"\\u2022 Customer ID: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: selectedCustomer.id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 40\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 21\n                }, this), Object.entries(selectedCustomer.cloud_credentials || {}).map(([cloud, cred]) => cred.configured && /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [\"\\u2022 \", cloud.toUpperCase(), \" credentials\"]\n                }, cloud, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 25\n                }, this)), !Object.values(selectedCustomer.cloud_credentials || {}).some(cred => cred.configured) && /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2022 No cloud credentials configured\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-600 dark:text-red-400 text-sm mt-3 font-medium\",\n              children: \"\\u26A0\\uFE0F This action cannot be undone.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowDeleteConfirm(false),\n              className: \"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700 transition-colors\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleDeleteCustomer(selectedCustomer.id),\n              disabled: loading,\n              className: \"px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\",\n              children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"w-4 h-4 animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 23\n                }, this), \"Deleting...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 23\n                }, this), \"Delete Customer\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"jKpu1l1bzg4nDHiBj98cJe9Ff2E=\");\n_c = App;\nfunction Modal({\n  children,\n  onClose,\n  large\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-full ${large ? 'w-[32rem]' : 'w-[24rem]'} relative animate-fadeIn`,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200\",\n        onClick: onClose,\n        children: /*#__PURE__*/_jsxDEV(X, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this), children]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 449,\n    columnNumber: 5\n  }, this);\n}\n_c2 = Modal;\nfunction CredentialForm({\n  cloud,\n  schema,\n  onSubmit,\n  onCancel\n}) {\n  _s2();\n  var _schema$required, _schema$optional;\n  const [formData, setFormData] = useState({});\n  const [fileFeedback, setFileFeedback] = useState('');\n  const handleSubmit = e => {\n    e.preventDefault();\n    onSubmit(formData);\n  };\n  const handleChange = (field, value) => {\n    setFormData({\n      ...formData,\n      [field]: value\n    });\n  };\n  const renderField = (field, required) => {\n    if (cloud === 'gcp' && field === 'private_key') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium mb-1\",\n          children: [field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()), required && '*']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: field,\n          required: required,\n          rows: 8,\n          placeholder: \"-----BEGIN PRIVATE KEY-----\\\\n...\\\\n-----END PRIVATE KEY-----\",\n          className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n          onChange: e => handleChange(field, e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this)]\n      }, field, true, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium mb-1\",\n        children: [field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()), required && '*']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: field.includes('secret') || field.includes('password') ? 'password' : 'text',\n        name: field,\n        required: required,\n        className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n        onChange: e => handleChange(field, e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this)]\n    }, field, true, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 7\n    }, this);\n  };\n  if (cloud === 'gcp') {\n    const handleFileUpload = e => {\n      const file = e.target.files[0];\n      if (file) {\n        const reader = new FileReader();\n        reader.onload = event => {\n          try {\n            const json = JSON.parse(event.target.result);\n            setFormData(json);\n            setFileFeedback(`File loaded: ${file.name}`);\n          } catch (err) {\n            setFileFeedback('Invalid JSON file');\n          }\n        };\n        reader.readAsText(file);\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-50 dark:bg-blue-900/20 p-3 rounded text-sm text-blue-700 dark:text-blue-200 mb-2\",\n        children: \"Upload your GCP service account JSON key file.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium mb-1\",\n          children: \"Service Account JSON File\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          accept: \".json\",\n          required: true,\n          onChange: handleFileUpload,\n          className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 text-xs text-green-600 dark:text-green-300\",\n          children: fileFeedback\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end gap-2 mt-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700\",\n          children: \"Save Credentials\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    className: \"space-y-4\",\n    children: [schema === null || schema === void 0 ? void 0 : (_schema$required = schema.required) === null || _schema$required === void 0 ? void 0 : _schema$required.map(field => renderField(field, true)), schema === null || schema === void 0 ? void 0 : (_schema$optional = schema.optional) === null || _schema$optional === void 0 ? void 0 : _schema$optional.map(field => renderField(field, false)), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end gap-2 mt-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: onCancel,\n        className: \"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700\",\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700\",\n        children: \"Save Credentials\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 543,\n    columnNumber: 5\n  }, this);\n}\n_s2(CredentialForm, \"9SsinILHtEsjwFFXZvHXapiNMd0=\");\n_c3 = CredentialForm;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"App\");\n$RefreshReg$(_c2, \"Modal\");\n$RefreshReg$(_c3, \"CredentialForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Users", "UserPlus", "Cloud", "Trash2", "KeyRound", "X", "Plus", "Loader2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "_window$RUNTIME_CONFI", "window", "RUNTIME_CONFIG", "process", "env", "REACT_APP_API_BASE_URL", "App", "_s", "customers", "setCustomers", "selectedCustomer", "setSelectedCustomer", "showAddCustomer", "setShowAddCustomer", "showAddCredentials", "setShowAddCredentials", "showDeleteConfirm", "setShowDeleteConfirm", "selectedCloud", "setSelectedCloud", "credentialSchemas", "setCredentialSchemas", "loading", "setLoading", "error", "setError", "useTestEndpoints", "setUseTestEndpoints", "darkMode", "setDarkMode", "saved", "localStorage", "getItem", "JSON", "parse", "fetchCustomers", "fetchCredentialSchemas", "document", "documentElement", "classList", "add", "remove", "setItem", "stringify", "endpoint", "response", "fetch", "data", "json", "status", "length", "err", "message", "schemas", "handleAddCustomer", "customerData", "method", "headers", "body", "handleDeleteCustomer", "customerId", "handleAddCredentials", "credentialData", "id", "handleDeleteCredentials", "cloud", "confirm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "checked", "onChange", "e", "target", "onClick", "d", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "disabled", "map", "customer", "name", "_customer$cloud_crede", "cloud_credentials", "configured", "toUpperCase", "_selectedCustomer$clo", "_selectedCustomer$clo2", "_selectedCustomer$clo3", "Modal", "onClose", "onSubmit", "preventDefault", "formData", "FormData", "get", "undefined", "required", "placeholder", "large", "CredentialForm", "schema", "onCancel", "Object", "entries", "cred", "values", "some", "_c", "_c2", "_s2", "_schema$required", "_schema$optional", "setFormData", "fileFeedback", "setFileFeedback", "handleSubmit", "handleChange", "field", "value", "renderField", "replace", "l", "rows", "includes", "handleFileUpload", "file", "files", "reader", "FileReader", "onload", "event", "result", "readAsText", "accept", "optional", "_c3", "$RefreshReg$"], "sources": ["/Users/<USER>/steampipe-compliance-unified/custom/admin-ui/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Users, UserPlus, Cloud, Trash2, KeyRound, X, Plus, Loader2, AlertTriangle } from 'lucide-react';\nimport './index.css'; // Tailwind base\n\nconst API_BASE_URL = window.RUNTIME_CONFIG?.API_BASE_URL || \n                     process.env.REACT_APP_API_BASE_URL || \n                     'http://localhost:8084';\n\nfunction App() {\n  const [customers, setCustomers] = useState([]);\n  const [selectedCustomer, setSelectedCustomer] = useState(null);\n  const [showAddCustomer, setShowAddCustomer] = useState(false);\n  const [showAddCredentials, setShowAddCredentials] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [selectedCloud, setSelectedCloud] = useState('');\n  const [credentialSchemas, setCredentialSchemas] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [useTestEndpoints, setUseTestEndpoints] = useState(true);\n  const [darkMode, setDarkMode] = useState(() => {\n    if (typeof window !== 'undefined') {\n      const saved = localStorage.getItem('darkMode');\n      return saved ? JSON.parse(saved) : false;\n    }\n    return false;\n  });\n\n  useEffect(() => {\n    fetchCustomers();\n    fetchCredentialSchemas();\n  }, []);\n\n  useEffect(() => {\n    if (darkMode) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n    localStorage.setItem('darkMode', JSON.stringify(darkMode));\n  }, [darkMode]);\n\n  const fetchCustomers = async () => {\n    try {\n      setLoading(true);\n      const endpoint = useTestEndpoints ? '/api/admin/test/customers' : '/api/admin/customers';\n      const response = await fetch(`${API_BASE_URL}${endpoint}`);\n      const data = await response.json();\n      if (data.status === 'success') {\n        setCustomers(data.customers);\n        // Auto-select the first customer if none is selected\n        if (data.customers.length > 0 && !selectedCustomer) {\n          setSelectedCustomer(data.customers[0]);\n        }\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to fetch customers: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchCredentialSchemas = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/api/admin/credential-schemas`);\n      const data = await response.json();\n      if (data.status === 'success') {\n        setCredentialSchemas(data.schemas);\n      }\n    } catch (err) {\n      // ignore\n    }\n  };\n\n  const handleAddCustomer = async (customerData) => {\n    try {\n      setLoading(true);\n      const endpoint = useTestEndpoints ? '/api/admin/test/customers' : '/api/admin/customers';\n      const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(customerData),\n      });\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n        setShowAddCustomer(false);\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to add customer: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteCustomer = async (customerId) => {\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/admin/customers/${customerId}`, { method: 'DELETE' });\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n        setSelectedCustomer(null);\n        setShowDeleteConfirm(false);\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to delete customer');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAddCredentials = async (credentialData) => {\n    if (!selectedCustomer || !selectedCloud) return;\n    try {\n      setLoading(true);\n      const endpoint = useTestEndpoints \n        ? `/api/admin/test/customers/${selectedCustomer.id}/credentials/${selectedCloud}`\n        : `/api/admin/customers/${selectedCustomer.id}/credentials/${selectedCloud}`;\n      const response = await fetch(\n        `${API_BASE_URL}${endpoint}`,\n        {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify(credentialData),\n        }\n      );\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n        setShowAddCredentials(false);\n        setSelectedCloud('');\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to add credentials');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteCredentials = async (customerId, cloud) => {\n    if (!window.confirm(`Are you sure you want to delete ${cloud} credentials?`)) return;\n    try {\n      setLoading(true);\n      const response = await fetch(\n        `${API_BASE_URL}/api/admin/customers/${customerId}/credentials/${cloud}`,\n        { method: 'DELETE' }\n      );\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to delete credentials');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Also, if the customer list changes (e.g. after add/delete), auto-select the first if none is selected\n  useEffect(() => {\n    if (customers.length > 0 && !selectedCustomer) {\n      setSelectedCustomer(customers[0]);\n    }\n  }, [customers, selectedCustomer]);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 transition-colors duration-200\">\n      <header className=\"bg-blue-700 text-white px-8 py-4 flex justify-between items-center shadow\">\n        <h1 className=\"text-2xl font-semibold flex items-center gap-2\">\n          <Users className=\"w-7 h-7\" />\n          Steampipe Compliance Admin\n        </h1>\n        <div className=\"flex items-center gap-4\">\n          <label className=\"flex items-center gap-2 cursor-pointer text-sm\">\n            <input\n              type=\"checkbox\"\n              checked={useTestEndpoints}\n              onChange={(e) => {\n                setUseTestEndpoints(e.target.checked);\n                fetchCustomers();\n              }}\n              className=\"accent-blue-600\"\n            />\n            Use Test Endpoints (No Auth)\n          </label>\n          <button\n            onClick={() => setDarkMode((d) => !d)}\n            className=\"p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200\"\n            aria-label={darkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n          >\n            {darkMode ? (\n              <svg className=\"w-5 h-5 text-yellow-500\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2} viewBox=\"0 0 24 24\"><path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 3v1m0 16v1m8.66-13.66l-.71.71M4.05 19.07l-.71.71M21 12h-1M4 12H3m16.66 5.66l-.71-.71M4.05 4.93l-.71-.71M12 5a7 7 0 100 14 7 7 0 000-14z\" /></svg>\n            ) : (\n              <svg className=\"w-5 h-5 text-gray-700\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2} viewBox=\"0 0 24 24\"><path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M21 12.79A9 9 0 1111.21 3a7 7 0 109.79 9.79z\" /></svg>\n            )}\n          </button>\n        </div>\n      </header>\n\n      <main className=\"max-w-5xl mx-auto py-8 px-4 flex flex-col min-h-[80vh] justify-center items-center\">\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-500 rounded-md flex items-start gap-3 animate-fadeIn\">\n            <X className=\"w-5 h-5 mt-0.5 flex-shrink-0 cursor-pointer\" onClick={() => setError(null)} />\n            <div>\n              <p className=\"font-medium\">{error}</p>\n            </div>\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-4xl items-start\">\n          {customers.length === 0 ? (\n            // Empty state when no customers\n            <div className=\"col-span-1 md:col-span-2 flex justify-center\">\n              <div className=\"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-12 transition-all duration-300 hover:shadow-3xl text-center max-w-md\">\n                <div className=\"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-6\">\n                  <Users className=\"h-8 w-8 text-blue-600 dark:text-blue-400\" />\n                </div>\n                <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">No Customers Found</h3>\n                <p className=\"text-gray-500 dark:text-gray-400 mb-6\">\n                  Get started by creating your first customer to manage their cloud compliance credentials.\n                </p>\n                <button\n                  className=\"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-full shadow-lg hover:from-indigo-600 hover:to-blue-600 transition mx-auto\"\n                  onClick={() => setShowAddCustomer(true)}\n                  disabled={loading}\n                >\n                  <UserPlus className=\"w-5 h-5\" /> Add Your First Customer\n                </button>\n              </div>\n            </div>\n          ) : (\n            <>\n              {/* Customers List */}\n              <section className=\"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-8 transition-all duration-300 hover:shadow-3xl flex flex-col\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2\">\n                    <Users className=\"w-5 h-5 text-blue-600\" /> Customers\n                  </h2>\n                  <button\n                    className=\"flex items-center gap-2 px-5 py-2 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-full shadow-lg hover:from-indigo-600 hover:to-blue-600 transition\"\n                    onClick={() => setShowAddCustomer(true)}\n                    disabled={loading}\n                  >\n                    <UserPlus className=\"w-4 h-4\" /> Add Customer\n                  </button>\n                </div>\n                {loading && <div className=\"flex items-center gap-2 text-gray-500\"><Loader2 className=\"w-4 h-4 animate-spin\" /> Loading...</div>}\n                <div className=\"flex flex-col gap-3 max-h-65 overflow-y-auto\">\n                  {customers.map((customer) => (\n                    <div\n                      key={customer.id}\n                      className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${selectedCustomer?.id === customer.id ? 'border-blue-600 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700'}`}\n                      onClick={() => setSelectedCustomer(customer)}\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">{customer.name}</h3>\n                          <p className=\"text-xs text-gray-500 dark:text-gray-400\">ID: {customer.id}</p>\n                        </div>\n                        <div className=\"flex gap-2\">\n                          {['aws', 'azure', 'gcp'].map((cloud) => (\n                            <span\n                              key={cloud}\n                              className={`px-2 py-1 rounded-full text-xs font-semibold ${customer.cloud_credentials[cloud]?.configured ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'}`}\n                            >\n                              {cloud.toUpperCase()}\n                            </span>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </section>\n\n              {/* Customer Details */}\n              {selectedCustomer && (\n                <section className=\"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-8 transition-all duration-300 hover:shadow-3xl\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h2 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2\">\n                      <UserPlus className=\"w-5 h-5 text-indigo-600\" /> {selectedCustomer.name}\n                    </h2>\n                    <button\n                      className=\"flex items-center gap-2 px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-all duration-200 shadow\"\n                      onClick={() => setShowDeleteConfirm(true)}\n                    >\n                      <Trash2 className=\"w-4 h-4\" /> Delete Customer\n                    </button>\n                  </div>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 mb-4\">ID: {selectedCustomer.id}</p>\n                  <div className=\"space-y-4\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2\">\n                      <KeyRound className=\"w-5 h-5 text-indigo-600\" /> Cloud Credentials\n                    </h3>\n                    {['aws', 'azure', 'gcp'].map((cloud) => (\n                      <div key={cloud} className=\"flex items-center justify-between border rounded-lg p-3 bg-gray-50 dark:bg-gray-700\">\n                        <div className=\"flex items-center gap-2\">\n                          <Cloud className=\"w-4 h-4 text-blue-500\" />\n                          <span className=\"font-medium text-gray-900 dark:text-white\">{cloud.toUpperCase()}</span>\n                          <span className={`ml-2 text-xs font-semibold ${selectedCustomer.cloud_credentials[cloud]?.configured ? 'text-green-600' : 'text-red-600'}`}>\n                            {selectedCustomer.cloud_credentials[cloud]?.configured ? '✓ Configured' : '✗ Not configured'}\n                          </span>\n                        </div>\n                        {selectedCustomer.cloud_credentials[cloud]?.configured ? (\n                          <button\n                            className=\"flex items-center gap-1 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 text-xs\"\n                            onClick={() => handleDeleteCredentials(selectedCustomer.id, cloud)}\n                          >\n                            <Trash2 className=\"w-3 h-3\" /> Delete\n                          </button>\n                        ) : (\n                          <button\n                            className=\"flex items-center gap-1 px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 text-xs\"\n                            onClick={() => { setSelectedCloud(cloud); setShowAddCredentials(true); }}\n                          >\n                            <Plus className=\"w-3 h-3\" /> Add\n                          </button>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </section>\n              )}\n            </>\n          )}\n        </div>\n\n        {/* Add Customer Modal */}\n        {showAddCustomer && (\n          <Modal onClose={() => setShowAddCustomer(false)}>\n            <h2 className=\"text-xl font-bold mb-4 flex items-center gap-2 text-gray-900 dark:text-white\"><UserPlus className=\"w-5 h-5 text-indigo-600\" /> Add New Customer</h2>\n            <form\n              onSubmit={(e) => {\n                e.preventDefault();\n                const formData = new FormData(e.target);\n                handleAddCustomer({\n                  name: formData.get('name'),\n                  id: formData.get('id') || undefined,\n                });\n              }}\n              className=\"space-y-4\"\n            >\n              <div>\n                <label className=\"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200\">Customer Name*</label>\n                <input type=\"text\" name=\"name\" required className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white text-gray-900 dark:bg-gray-900 dark:text-white dark:border-gray-700\" />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200\">Customer ID (optional)</label>\n                <input type=\"text\" name=\"id\" placeholder=\"Auto-generated if empty\" className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white text-gray-900 dark:bg-gray-900 dark:text-white dark:border-gray-700\" />\n              </div>\n              <div className=\"flex justify-end gap-2 mt-6\">\n                <button type=\"button\" onClick={() => setShowAddCustomer(false)} className=\"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700\">Cancel</button>\n                <button type=\"submit\" className=\"px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700\">Add Customer</button>\n              </div>\n            </form>\n          </Modal>\n        )}\n\n        {/* Add Credentials Modal */}\n        {showAddCredentials && selectedCloud && (\n          <Modal onClose={() => setShowAddCredentials(false)} large>\n            <h2 className=\"text-xl font-bold mb-4 flex items-center gap-2\"><KeyRound className=\"w-5 h-5 text-indigo-600\" /> Add {selectedCloud.toUpperCase()} Credentials</h2>\n            <CredentialForm\n              cloud={selectedCloud}\n              schema={credentialSchemas[selectedCloud]}\n              onSubmit={handleAddCredentials}\n              onCancel={() => setShowAddCredentials(false)}\n            />\n          </Modal>\n        )}\n\n        {/* Delete Confirmation Modal */}\n        {showDeleteConfirm && selectedCustomer && (\n          <Modal onClose={() => setShowDeleteConfirm(false)}>\n            <div className=\"text-center\">\n              <div className=\"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 mb-4\">\n                <AlertTriangle className=\"h-6 w-6 text-red-600 dark:text-red-400\" />\n              </div>\n              <h2 className=\"text-xl font-bold mb-4 text-gray-900 dark:text-white\">Delete Customer</h2>\n              <div className=\"text-left mb-6\">\n                <p className=\"text-gray-700 dark:text-gray-300 mb-4\">\n                  Are you sure you want to delete <span className=\"font-semibold text-red-600 dark:text-red-400\">{selectedCustomer.name}</span>?\n                </p>\n                <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg p-4\">\n                  <h4 className=\"font-semibold text-red-800 dark:text-red-300 mb-2\">This action will permanently delete:</h4>\n                  <ul className=\"text-sm text-red-700 dark:text-red-300 space-y-1\">\n                    <li>• Customer: <span className=\"font-medium\">{selectedCustomer.name}</span></li>\n                    <li>• Customer ID: <span className=\"font-medium\">{selectedCustomer.id}</span></li>\n                    {Object.entries(selectedCustomer.cloud_credentials || {}).map(([cloud, cred]) => \n                      cred.configured && (\n                        <li key={cloud}>• {cloud.toUpperCase()} credentials</li>\n                      )\n                    )}\n                    {!Object.values(selectedCustomer.cloud_credentials || {}).some(cred => cred.configured) && (\n                      <li>• No cloud credentials configured</li>\n                    )}\n                  </ul>\n                </div>\n                <p className=\"text-red-600 dark:text-red-400 text-sm mt-3 font-medium\">\n                  ⚠️ This action cannot be undone.\n                </p>\n              </div>\n              <div className=\"flex justify-end gap-3\">\n                <button \n                  type=\"button\" \n                  onClick={() => setShowDeleteConfirm(false)} \n                  className=\"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700 transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button \n                  onClick={() => handleDeleteCustomer(selectedCustomer.id)}\n                  disabled={loading}\n                  className=\"px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2\"\n                >\n                  {loading ? (\n                    <>\n                      <Loader2 className=\"w-4 h-4 animate-spin\" />\n                      Deleting...\n                    </>\n                  ) : (\n                    <>\n                      <Trash2 className=\"w-4 h-4\" />\n                      Delete Customer\n                    </>\n                  )}\n                </button>\n              </div>\n            </div>\n          </Modal>\n        )}\n      </main>\n    </div>\n  );\n}\n\nfunction Modal({ children, onClose, large }) {\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\">\n      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-full ${large ? 'w-[32rem]' : 'w-[24rem]'} relative animate-fadeIn`}>\n        <button className=\"absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200\" onClick={onClose}>\n          <X className=\"w-5 h-5\" />\n        </button>\n        {children}\n      </div>\n    </div>\n  );\n}\n\nfunction CredentialForm({ cloud, schema, onSubmit, onCancel }) {\n  const [formData, setFormData] = useState({});\n  const [fileFeedback, setFileFeedback] = useState('');\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onSubmit(formData);\n  };\n\n  const handleChange = (field, value) => {\n    setFormData({ ...formData, [field]: value });\n  };\n\n  const renderField = (field, required) => {\n    if (cloud === 'gcp' && field === 'private_key') {\n      return (\n        <div key={field} className=\"mb-4\">\n          <label className=\"block text-sm font-medium mb-1\">{field.replace(/_/g, ' ').replace(/\\b\\w/g, (l) => l.toUpperCase())}{required && '*'}</label>\n          <textarea\n            name={field}\n            required={required}\n            rows={8}\n            placeholder=\"-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\"\n            className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n            onChange={(e) => handleChange(field, e.target.value)}\n          />\n        </div>\n      );\n    }\n    return (\n      <div key={field} className=\"mb-4\">\n        <label className=\"block text-sm font-medium mb-1\">{field.replace(/_/g, ' ').replace(/\\b\\w/g, (l) => l.toUpperCase())}{required && '*'}</label>\n        <input\n          type={field.includes('secret') || field.includes('password') ? 'password' : 'text'}\n          name={field}\n          required={required}\n          className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n          onChange={(e) => handleChange(field, e.target.value)}\n        />\n      </div>\n    );\n  };\n\n  if (cloud === 'gcp') {\n    const handleFileUpload = (e) => {\n      const file = e.target.files[0];\n      if (file) {\n        const reader = new FileReader();\n        reader.onload = (event) => {\n          try {\n            const json = JSON.parse(event.target.result);\n            setFormData(json);\n            setFileFeedback(`File loaded: ${file.name}`);\n          } catch (err) {\n            setFileFeedback('Invalid JSON file');\n          }\n        };\n        reader.readAsText(file);\n      }\n    };\n    return (\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 p-3 rounded text-sm text-blue-700 dark:text-blue-200 mb-2\">Upload your GCP service account JSON key file.</div>\n        <div className=\"mb-4\">\n          <label className=\"block text-sm font-medium mb-1\">Service Account JSON File</label>\n          <input\n            type=\"file\"\n            accept=\".json\"\n            required\n            onChange={handleFileUpload}\n            className=\"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\"\n          />\n          <div className=\"mt-2 text-xs text-green-600 dark:text-green-300\">{fileFeedback}</div>\n        </div>\n        <div className=\"flex justify-end gap-2 mt-6\">\n          <button type=\"button\" onClick={onCancel} className=\"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700\">Cancel</button>\n          <button type=\"submit\" className=\"px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700\">Save Credentials</button>\n        </div>\n      </form>\n    );\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\n      {schema?.required?.map((field) => renderField(field, true))}\n      {schema?.optional?.map((field) => renderField(field, false))}\n      <div className=\"flex justify-end gap-2 mt-6\">\n        <button type=\"button\" onClick={onCancel} className=\"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700\">Cancel</button>\n        <button type=\"submit\" className=\"px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700\">Save Credentials</button>\n      </div>\n    </form>\n  );\n}\n\nexport default App;"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,CAAC,EAAEC,IAAI,EAAEC,OAAO,EAAEC,aAAa,QAAQ,cAAc;AACxG,OAAO,aAAa,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,YAAY,GAAG,EAAAC,qBAAA,GAAAC,MAAM,CAACC,cAAc,cAAAF,qBAAA,uBAArBA,qBAAA,CAAuBD,YAAY,KACnCI,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAClC,uBAAuB;AAE5C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACgC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,MAAM;IAC7C,IAAI,OAAOiB,MAAM,KAAK,WAAW,EAAE;MACjC,MAAM6B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MAC9C,OAAOF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,KAAK;IAC1C;IACA,OAAO,KAAK;EACd,CAAC,CAAC;EAEF7C,SAAS,CAAC,MAAM;IACdkD,cAAc,CAAC,CAAC;IAChBC,sBAAsB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAENnD,SAAS,CAAC,MAAM;IACd,IAAI2C,QAAQ,EAAE;MACZS,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;IAChD,CAAC,MAAM;MACLH,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACE,MAAM,CAAC,MAAM,CAAC;IACnD;IACAV,YAAY,CAACW,OAAO,CAAC,UAAU,EAAET,IAAI,CAACU,SAAS,CAACf,QAAQ,CAAC,CAAC;EAC5D,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,MAAMO,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqB,QAAQ,GAAGlB,gBAAgB,GAAG,2BAA2B,GAAG,sBAAsB;MACxF,MAAMmB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG/C,YAAY,GAAG6C,QAAQ,EAAE,CAAC;MAC1D,MAAMG,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,MAAM,KAAK,SAAS,EAAE;QAC7BxC,YAAY,CAACsC,IAAI,CAACvC,SAAS,CAAC;QAC5B;QACA,IAAIuC,IAAI,CAACvC,SAAS,CAAC0C,MAAM,GAAG,CAAC,IAAI,CAACxC,gBAAgB,EAAE;UAClDC,mBAAmB,CAACoC,IAAI,CAACvC,SAAS,CAAC,CAAC,CAAC,CAAC;QACxC;MACF,CAAC,MAAM;QACLiB,QAAQ,CAACsB,IAAI,CAACvB,KAAK,CAAC;MACtB;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ1B,QAAQ,CAAC,6BAA6B,GAAG0B,GAAG,CAACC,OAAO,CAAC;IACvD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG/C,YAAY,+BAA+B,CAAC;MAC5E,MAAMgD,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,MAAM,KAAK,SAAS,EAAE;QAC7B5B,oBAAoB,CAAC0B,IAAI,CAACM,OAAO,CAAC;MACpC;IACF,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZ;IAAA;EAEJ,CAAC;EAED,MAAMG,iBAAiB,GAAG,MAAOC,YAAY,IAAK;IAChD,IAAI;MACFhC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqB,QAAQ,GAAGlB,gBAAgB,GAAG,2BAA2B,GAAG,sBAAsB;MACxF,MAAMmB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG/C,YAAY,GAAG6C,QAAQ,EAAE,EAAE;QACzDY,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEzB,IAAI,CAACU,SAAS,CAACY,YAAY;MACnC,CAAC,CAAC;MACF,MAAMR,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,MAAM,KAAK,SAAS,EAAE;QAC7B,MAAMd,cAAc,CAAC,CAAC;QACtBtB,kBAAkB,CAAC,KAAK,CAAC;MAC3B,CAAC,MAAM;QACLY,QAAQ,CAACsB,IAAI,CAACvB,KAAK,CAAC;MACtB;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ1B,QAAQ,CAAC,0BAA0B,GAAG0B,GAAG,CAACC,OAAO,CAAC;IACpD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,oBAAoB,GAAG,MAAOC,UAAU,IAAK;IACjD,IAAI;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG/C,YAAY,wBAAwB6D,UAAU,EAAE,EAAE;QAAEJ,MAAM,EAAE;MAAS,CAAC,CAAC;MACvG,MAAMT,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,MAAM,KAAK,SAAS,EAAE;QAC7B,MAAMd,cAAc,CAAC,CAAC;QACtBxB,mBAAmB,CAAC,IAAI,CAAC;QACzBM,oBAAoB,CAAC,KAAK,CAAC;MAC7B,CAAC,MAAM;QACLQ,QAAQ,CAACsB,IAAI,CAACvB,KAAK,CAAC;MACtB;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ1B,QAAQ,CAAC,2BAA2B,CAAC;IACvC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,oBAAoB,GAAG,MAAOC,cAAc,IAAK;IACrD,IAAI,CAACpD,gBAAgB,IAAI,CAACQ,aAAa,EAAE;IACzC,IAAI;MACFK,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqB,QAAQ,GAAGlB,gBAAgB,GAC7B,6BAA6BhB,gBAAgB,CAACqD,EAAE,gBAAgB7C,aAAa,EAAE,GAC/E,wBAAwBR,gBAAgB,CAACqD,EAAE,gBAAgB7C,aAAa,EAAE;MAC9E,MAAM2B,QAAQ,GAAG,MAAMC,KAAK,CAC1B,GAAG/C,YAAY,GAAG6C,QAAQ,EAAE,EAC5B;QACEY,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEzB,IAAI,CAACU,SAAS,CAACmB,cAAc;MACrC,CACF,CAAC;MACD,MAAMf,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,MAAM,KAAK,SAAS,EAAE;QAC7B,MAAMd,cAAc,CAAC,CAAC;QACtBpB,qBAAqB,CAAC,KAAK,CAAC;QAC5BI,gBAAgB,CAAC,EAAE,CAAC;MACtB,CAAC,MAAM;QACLM,QAAQ,CAACsB,IAAI,CAACvB,KAAK,CAAC;MACtB;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ1B,QAAQ,CAAC,2BAA2B,CAAC;IACvC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyC,uBAAuB,GAAG,MAAAA,CAAOJ,UAAU,EAAEK,KAAK,KAAK;IAC3D,IAAI,CAAChE,MAAM,CAACiE,OAAO,CAAC,mCAAmCD,KAAK,eAAe,CAAC,EAAE;IAC9E,IAAI;MACF1C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsB,QAAQ,GAAG,MAAMC,KAAK,CAC1B,GAAG/C,YAAY,wBAAwB6D,UAAU,gBAAgBK,KAAK,EAAE,EACxE;QAAET,MAAM,EAAE;MAAS,CACrB,CAAC;MACD,MAAMT,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,MAAM,KAAK,SAAS,EAAE;QAC7B,MAAMd,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLV,QAAQ,CAACsB,IAAI,CAACvB,KAAK,CAAC;MACtB;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ1B,QAAQ,CAAC,8BAA8B,CAAC;IAC1C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAtC,SAAS,CAAC,MAAM;IACd,IAAIuB,SAAS,CAAC0C,MAAM,GAAG,CAAC,IAAI,CAACxC,gBAAgB,EAAE;MAC7CC,mBAAmB,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACA,SAAS,EAAEE,gBAAgB,CAAC,CAAC;EAEjC,oBACEd,OAAA;IAAKuE,SAAS,EAAC,wGAAwG;IAAAC,QAAA,gBACrHxE,OAAA;MAAQuE,SAAS,EAAC,2EAA2E;MAAAC,QAAA,gBAC3FxE,OAAA;QAAIuE,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC5DxE,OAAA,CAACV,KAAK;UAACiF,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,8BAE/B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL5E,OAAA;QAAKuE,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCxE,OAAA;UAAOuE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC/DxE,OAAA;YACE6E,IAAI,EAAC,UAAU;YACfC,OAAO,EAAEhD,gBAAiB;YAC1BiD,QAAQ,EAAGC,CAAC,IAAK;cACfjD,mBAAmB,CAACiD,CAAC,CAACC,MAAM,CAACH,OAAO,CAAC;cACrCvC,cAAc,CAAC,CAAC;YAClB,CAAE;YACFgC,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,gCAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR5E,OAAA;UACEkF,OAAO,EAAEA,CAAA,KAAMjD,WAAW,CAAEkD,CAAC,IAAK,CAACA,CAAC,CAAE;UACtCZ,SAAS,EAAC,qHAAqH;UAC/H,cAAYvC,QAAQ,GAAG,sBAAsB,GAAG,qBAAsB;UAAAwC,QAAA,EAErExC,QAAQ,gBACPhC,OAAA;YAAKuE,SAAS,EAAC,yBAAyB;YAACa,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAE,CAAE;YAACC,OAAO,EAAC,WAAW;YAAAf,QAAA,eAACxE,OAAA;cAAMwF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACN,CAAC,EAAC;YAA6I;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAE1T5E,OAAA;YAAKuE,SAAS,EAAC,uBAAuB;YAACa,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAE,CAAE;YAACC,OAAO,EAAC,WAAW;YAAAf,QAAA,eAACxE,OAAA;cAAMwF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACN,CAAC,EAAC;YAA8C;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACzN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET5E,OAAA;MAAMuE,SAAS,EAAC,oFAAoF;MAAAC,QAAA,GACjG5C,KAAK,iBACJ5B,OAAA;QAAKuE,SAAS,EAAC,qGAAqG;QAAAC,QAAA,gBAClHxE,OAAA,CAACL,CAAC;UAAC4E,SAAS,EAAC,6CAA6C;UAACW,OAAO,EAAEA,CAAA,KAAMrD,QAAQ,CAAC,IAAI;QAAE;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5F5E,OAAA;UAAAwE,QAAA,eACExE,OAAA;YAAGuE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE5C;UAAK;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED5E,OAAA;QAAKuE,SAAS,EAAC,oEAAoE;QAAAC,QAAA,EAChF5D,SAAS,CAAC0C,MAAM,KAAK,CAAC;QAAA;QACrB;QACAtD,OAAA;UAAKuE,SAAS,EAAC,8CAA8C;UAAAC,QAAA,eAC3DxE,OAAA;YAAKuE,SAAS,EAAC,wKAAwK;YAAAC,QAAA,gBACrLxE,OAAA;cAAKuE,SAAS,EAAC,sGAAsG;cAAAC,QAAA,eACnHxE,OAAA,CAACV,KAAK;gBAACiF,SAAS,EAAC;cAA0C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACN5E,OAAA;cAAIuE,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5F5E,OAAA;cAAGuE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ5E,OAAA;cACEuE,SAAS,EAAC,6KAA6K;cACvLW,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,IAAI,CAAE;cACxCyE,QAAQ,EAAEhE,OAAQ;cAAA8C,QAAA,gBAElBxE,OAAA,CAACT,QAAQ;gBAACgF,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAClC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN5E,OAAA,CAAAE,SAAA;UAAAsE,QAAA,gBAEExE,OAAA;YAASuE,SAAS,EAAC,gKAAgK;YAAAC,QAAA,gBACjLxE,OAAA;cAAKuE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDxE,OAAA;gBAAIuE,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACrFxE,OAAA,CAACV,KAAK;kBAACiF,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,cAC7C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5E,OAAA;gBACEuE,SAAS,EAAC,qKAAqK;gBAC/KW,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,IAAI,CAAE;gBACxCyE,QAAQ,EAAEhE,OAAQ;gBAAA8C,QAAA,gBAElBxE,OAAA,CAACT,QAAQ;kBAACgF,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAClC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLlD,OAAO,iBAAI1B,OAAA;cAAKuE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAACxE,OAAA,CAACH,OAAO;gBAAC0E,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAAW;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChI5E,OAAA;cAAKuE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAC1D5D,SAAS,CAAC+E,GAAG,CAAEC,QAAQ,iBACtB5F,OAAA;gBAEEuE,SAAS,EAAE,oEAAoE,CAAAzD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqD,EAAE,MAAKyB,QAAQ,CAACzB,EAAE,GAAG,gDAAgD,GAAG,kEAAkE,EAAG;gBAC9Oe,OAAO,EAAEA,CAAA,KAAMnE,mBAAmB,CAAC6E,QAAQ,CAAE;gBAAApB,QAAA,eAE7CxE,OAAA;kBAAKuE,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDxE,OAAA;oBAAAwE,QAAA,gBACExE,OAAA;sBAAIuE,SAAS,EAAC,qDAAqD;sBAAAC,QAAA,EAAEoB,QAAQ,CAACC;oBAAI;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxF5E,OAAA;sBAAGuE,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,GAAC,MAAI,EAACoB,QAAQ,CAACzB,EAAE;oBAAA;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eACN5E,OAAA;oBAAKuE,SAAS,EAAC,YAAY;oBAAAC,QAAA,EACxB,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAACmB,GAAG,CAAEtB,KAAK;sBAAA,IAAAyB,qBAAA;sBAAA,oBACjC9F,OAAA;wBAEEuE,SAAS,EAAE,gDAAgD,CAAAuB,qBAAA,GAAAF,QAAQ,CAACG,iBAAiB,CAAC1B,KAAK,CAAC,cAAAyB,qBAAA,eAAjCA,qBAAA,CAAmCE,UAAU,GAAG,sEAAsE,GAAG,8DAA8D,EAAG;wBAAAxB,QAAA,EAEpPH,KAAK,CAAC4B,WAAW,CAAC;sBAAC,GAHf5B,KAAK;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAIN,CAAC;oBAAA,CACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAnBDgB,QAAQ,CAACzB,EAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGT9D,gBAAgB,iBACfd,OAAA;YAASuE,SAAS,EAAC,kJAAkJ;YAAAC,QAAA,gBACnKxE,OAAA;cAAKuE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDxE,OAAA;gBAAIuE,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,gBACrFxE,OAAA,CAACT,QAAQ;kBAACgF,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC9D,gBAAgB,CAAC+E,IAAI;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACL5E,OAAA;gBACEuE,SAAS,EAAC,wHAAwH;gBAClIW,OAAO,EAAEA,CAAA,KAAM7D,oBAAoB,CAAC,IAAI,CAAE;gBAAAmD,QAAA,gBAE1CxE,OAAA,CAACP,MAAM;kBAAC8E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oBAChC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN5E,OAAA;cAAGuE,SAAS,EAAC,+CAA+C;cAAAC,QAAA,GAAC,MAAI,EAAC1D,gBAAgB,CAACqD,EAAE;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1F5E,OAAA;cAAKuE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxE,OAAA;gBAAIuE,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,gBACzFxE,OAAA,CAACN,QAAQ;kBAAC6E,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sBAClD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACJ,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAACe,GAAG,CAAEtB,KAAK;gBAAA,IAAA6B,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;gBAAA,oBACjCpG,OAAA;kBAAiBuE,SAAS,EAAC,qFAAqF;kBAAAC,QAAA,gBAC9GxE,OAAA;oBAAKuE,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtCxE,OAAA,CAACR,KAAK;sBAAC+E,SAAS,EAAC;oBAAuB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3C5E,OAAA;sBAAMuE,SAAS,EAAC,2CAA2C;sBAAAC,QAAA,EAAEH,KAAK,CAAC4B,WAAW,CAAC;oBAAC;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACxF5E,OAAA;sBAAMuE,SAAS,EAAE,8BAA8B,CAAA2B,qBAAA,GAAApF,gBAAgB,CAACiF,iBAAiB,CAAC1B,KAAK,CAAC,cAAA6B,qBAAA,eAAzCA,qBAAA,CAA2CF,UAAU,GAAG,gBAAgB,GAAG,cAAc,EAAG;sBAAAxB,QAAA,EACxI,CAAA2B,sBAAA,GAAArF,gBAAgB,CAACiF,iBAAiB,CAAC1B,KAAK,CAAC,cAAA8B,sBAAA,eAAzCA,sBAAA,CAA2CH,UAAU,GAAG,cAAc,GAAG;oBAAkB;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EACL,CAAAwB,sBAAA,GAAAtF,gBAAgB,CAACiF,iBAAiB,CAAC1B,KAAK,CAAC,cAAA+B,sBAAA,eAAzCA,sBAAA,CAA2CJ,UAAU,gBACpDhG,OAAA;oBACEuE,SAAS,EAAC,4FAA4F;oBACtGW,OAAO,EAAEA,CAAA,KAAMd,uBAAuB,CAACtD,gBAAgB,CAACqD,EAAE,EAAEE,KAAK,CAAE;oBAAAG,QAAA,gBAEnExE,OAAA,CAACP,MAAM;sBAAC8E,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,WAChC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gBAET5E,OAAA;oBACEuE,SAAS,EAAC,kGAAkG;oBAC5GW,OAAO,EAAEA,CAAA,KAAM;sBAAE3D,gBAAgB,CAAC8C,KAAK,CAAC;sBAAElD,qBAAqB,CAAC,IAAI,CAAC;oBAAE,CAAE;oBAAAqD,QAAA,gBAEzExE,OAAA,CAACJ,IAAI;sBAAC2E,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAC9B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA,GAtBOP,KAAK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuBV,CAAC;cAAA,CACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACV;QAAA,eACD;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL5D,eAAe,iBACdhB,OAAA,CAACqG,KAAK;QAACC,OAAO,EAAEA,CAAA,KAAMrF,kBAAkB,CAAC,KAAK,CAAE;QAAAuD,QAAA,gBAC9CxE,OAAA;UAAIuE,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAACxE,OAAA,CAACT,QAAQ;YAACgF,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAAiB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnK5E,OAAA;UACEuG,QAAQ,EAAGvB,CAAC,IAAK;YACfA,CAAC,CAACwB,cAAc,CAAC,CAAC;YAClB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC1B,CAAC,CAACC,MAAM,CAAC;YACvCvB,iBAAiB,CAAC;cAChBmC,IAAI,EAAEY,QAAQ,CAACE,GAAG,CAAC,MAAM,CAAC;cAC1BxC,EAAE,EAAEsC,QAAQ,CAACE,GAAG,CAAC,IAAI,CAAC,IAAIC;YAC5B,CAAC,CAAC;UACJ,CAAE;UACFrC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAErBxE,OAAA;YAAAwE,QAAA,gBACExE,OAAA;cAAOuE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzG5E,OAAA;cAAO6E,IAAI,EAAC,MAAM;cAACgB,IAAI,EAAC,MAAM;cAACgB,QAAQ;cAACtC,SAAS,EAAC;YAAiL;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnO,CAAC,eACN5E,OAAA;YAAAwE,QAAA,gBACExE,OAAA;cAAOuE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjH5E,OAAA;cAAO6E,IAAI,EAAC,MAAM;cAACgB,IAAI,EAAC,IAAI;cAACiB,WAAW,EAAC,yBAAyB;cAACvC,SAAS,EAAC;YAAiL;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9P,CAAC,eACN5E,OAAA;YAAKuE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CxE,OAAA;cAAQ6E,IAAI,EAAC,QAAQ;cAACK,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,KAAK,CAAE;cAACsD,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzJ5E,OAAA;cAAQ6E,IAAI,EAAC,QAAQ;cAACN,SAAS,EAAC,gEAAgE;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACR,EAGA1D,kBAAkB,IAAII,aAAa,iBAClCtB,OAAA,CAACqG,KAAK;QAACC,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,KAAK,CAAE;QAAC4F,KAAK;QAAAvC,QAAA,gBACvDxE,OAAA;UAAIuE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAACxE,OAAA,CAACN,QAAQ;YAAC6E,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAAK,EAACtD,aAAa,CAAC2E,WAAW,CAAC,CAAC,EAAC,cAAY;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClK5E,OAAA,CAACgH,cAAc;UACb3C,KAAK,EAAE/C,aAAc;UACrB2F,MAAM,EAAEzF,iBAAiB,CAACF,aAAa,CAAE;UACzCiF,QAAQ,EAAEtC,oBAAqB;UAC/BiD,QAAQ,EAAEA,CAAA,KAAM/F,qBAAqB,CAAC,KAAK;QAAE;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACR,EAGAxD,iBAAiB,IAAIN,gBAAgB,iBACpCd,OAAA,CAACqG,KAAK;QAACC,OAAO,EAAEA,CAAA,KAAMjF,oBAAoB,CAAC,KAAK,CAAE;QAAAmD,QAAA,eAChDxE,OAAA;UAAKuE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BxE,OAAA;YAAKuE,SAAS,EAAC,oGAAoG;YAAAC,QAAA,eACjHxE,OAAA,CAACF,aAAa;cAACyE,SAAS,EAAC;YAAwC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACN5E,OAAA;YAAIuE,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzF5E,OAAA;YAAKuE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BxE,OAAA;cAAGuE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,GAAC,kCACnB,eAAAxE,OAAA;gBAAMuE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAE1D,gBAAgB,CAAC+E;cAAI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,KAC/H;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ5E,OAAA;cAAKuE,SAAS,EAAC,0FAA0F;cAAAC,QAAA,gBACvGxE,OAAA;gBAAIuE,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAC;cAAoC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3G5E,OAAA;gBAAIuE,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBAC9DxE,OAAA;kBAAAwE,QAAA,GAAI,mBAAY,eAAAxE,OAAA;oBAAMuE,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAE1D,gBAAgB,CAAC+E;kBAAI;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjF5E,OAAA;kBAAAwE,QAAA,GAAI,sBAAe,eAAAxE,OAAA;oBAAMuE,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAE1D,gBAAgB,CAACqD;kBAAE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACjFuC,MAAM,CAACC,OAAO,CAACtG,gBAAgB,CAACiF,iBAAiB,IAAI,CAAC,CAAC,CAAC,CAACJ,GAAG,CAAC,CAAC,CAACtB,KAAK,EAAEgD,IAAI,CAAC,KAC1EA,IAAI,CAACrB,UAAU,iBACbhG,OAAA;kBAAAwE,QAAA,GAAgB,SAAE,EAACH,KAAK,CAAC4B,WAAW,CAAC,CAAC,EAAC,cAAY;gBAAA,GAA1C5B,KAAK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAyC,CAE3D,CAAC,EACA,CAACuC,MAAM,CAACG,MAAM,CAACxG,gBAAgB,CAACiF,iBAAiB,IAAI,CAAC,CAAC,CAAC,CAACwB,IAAI,CAACF,IAAI,IAAIA,IAAI,CAACrB,UAAU,CAAC,iBACrFhG,OAAA;kBAAAwE,QAAA,EAAI;gBAAiC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC1C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACN5E,OAAA;cAAGuE,SAAS,EAAC,yDAAyD;cAAAC,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN5E,OAAA;YAAKuE,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCxE,OAAA;cACE6E,IAAI,EAAC,QAAQ;cACbK,OAAO,EAAEA,CAAA,KAAM7D,oBAAoB,CAAC,KAAK,CAAE;cAC3CkD,SAAS,EAAC,iFAAiF;cAAAC,QAAA,EAC5F;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT5E,OAAA;cACEkF,OAAO,EAAEA,CAAA,KAAMnB,oBAAoB,CAACjD,gBAAgB,CAACqD,EAAE,CAAE;cACzDuB,QAAQ,EAAEhE,OAAQ;cAClB6C,SAAS,EAAC,oJAAoJ;cAAAC,QAAA,EAE7J9C,OAAO,gBACN1B,OAAA,CAAAE,SAAA;gBAAAsE,QAAA,gBACExE,OAAA,CAACH,OAAO;kBAAC0E,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAE9C;cAAA,eAAE,CAAC,gBAEH5E,OAAA,CAAAE,SAAA;gBAAAsE,QAAA,gBACExE,OAAA,CAACP,MAAM;kBAAC8E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mBAEhC;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACjE,EAAA,CApbQD,GAAG;AAAA8G,EAAA,GAAH9G,GAAG;AAsbZ,SAAS2F,KAAKA,CAAC;EAAE7B,QAAQ;EAAE8B,OAAO;EAAES;AAAM,CAAC,EAAE;EAC3C,oBACE/G,OAAA;IAAKuE,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFxE,OAAA;MAAKuE,SAAS,EAAE,iEAAiEwC,KAAK,GAAG,WAAW,GAAG,WAAW,0BAA2B;MAAAvC,QAAA,gBAC3IxE,OAAA;QAAQuE,SAAS,EAAC,mFAAmF;QAACW,OAAO,EAAEoB,OAAQ;QAAA9B,QAAA,eACrHxE,OAAA,CAACL,CAAC;UAAC4E,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,EACRJ,QAAQ;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC6C,GAAA,GAXQpB,KAAK;AAad,SAASW,cAAcA,CAAC;EAAE3C,KAAK;EAAE4C,MAAM;EAAEV,QAAQ;EAAEW;AAAS,CAAC,EAAE;EAAAQ,GAAA;EAAA,IAAAC,gBAAA,EAAAC,gBAAA;EAC7D,MAAM,CAACnB,QAAQ,EAAEoB,WAAW,CAAC,GAAGzI,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAAC0I,YAAY,EAAEC,eAAe,CAAC,GAAG3I,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM4I,YAAY,GAAIhD,CAAC,IAAK;IAC1BA,CAAC,CAACwB,cAAc,CAAC,CAAC;IAClBD,QAAQ,CAACE,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMwB,YAAY,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrCN,WAAW,CAAC;MAAE,GAAGpB,QAAQ;MAAE,CAACyB,KAAK,GAAGC;IAAM,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMC,WAAW,GAAGA,CAACF,KAAK,EAAErB,QAAQ,KAAK;IACvC,IAAIxC,KAAK,KAAK,KAAK,IAAI6D,KAAK,KAAK,aAAa,EAAE;MAC9C,oBACElI,OAAA;QAAiBuE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBAC/BxE,OAAA;UAAOuE,SAAS,EAAC,gCAAgC;UAAAC,QAAA,GAAE0D,KAAK,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACrC,WAAW,CAAC,CAAC,CAAC,EAAEY,QAAQ,IAAI,GAAG;QAAA;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9I5E,OAAA;UACE6F,IAAI,EAAEqC,KAAM;UACZrB,QAAQ,EAAEA,QAAS;UACnB0B,IAAI,EAAE,CAAE;UACRzB,WAAW,EAAC,+DAA6D;UACzEvC,SAAS,EAAC,oGAAoG;UAC9GQ,QAAQ,EAAGC,CAAC,IAAKiD,YAAY,CAACC,KAAK,EAAElD,CAAC,CAACC,MAAM,CAACkD,KAAK;QAAE;UAAA1D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA,GATMsD,KAAK;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUV,CAAC;IAEV;IACA,oBACE5E,OAAA;MAAiBuE,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAC/BxE,OAAA;QAAOuE,SAAS,EAAC,gCAAgC;QAAAC,QAAA,GAAE0D,KAAK,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACrC,WAAW,CAAC,CAAC,CAAC,EAAEY,QAAQ,IAAI,GAAG;MAAA;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC9I5E,OAAA;QACE6E,IAAI,EAAEqD,KAAK,CAACM,QAAQ,CAAC,QAAQ,CAAC,IAAIN,KAAK,CAACM,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,MAAO;QACnF3C,IAAI,EAAEqC,KAAM;QACZrB,QAAQ,EAAEA,QAAS;QACnBtC,SAAS,EAAC,oGAAoG;QAC9GQ,QAAQ,EAAGC,CAAC,IAAKiD,YAAY,CAACC,KAAK,EAAElD,CAAC,CAACC,MAAM,CAACkD,KAAK;MAAE;QAAA1D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA,GARMsD,KAAK;MAAAzD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OASV,CAAC;EAEV,CAAC;EAED,IAAIP,KAAK,KAAK,KAAK,EAAE;IACnB,MAAMoE,gBAAgB,GAAIzD,CAAC,IAAK;MAC9B,MAAM0D,IAAI,GAAG1D,CAAC,CAACC,MAAM,CAAC0D,KAAK,CAAC,CAAC,CAAC;MAC9B,IAAID,IAAI,EAAE;QACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAIC,KAAK,IAAK;UACzB,IAAI;YACF,MAAM3F,IAAI,GAAGf,IAAI,CAACC,KAAK,CAACyG,KAAK,CAAC9D,MAAM,CAAC+D,MAAM,CAAC;YAC5CnB,WAAW,CAACzE,IAAI,CAAC;YACjB2E,eAAe,CAAC,gBAAgBW,IAAI,CAAC7C,IAAI,EAAE,CAAC;UAC9C,CAAC,CAAC,OAAOtC,GAAG,EAAE;YACZwE,eAAe,CAAC,mBAAmB,CAAC;UACtC;QACF,CAAC;QACDa,MAAM,CAACK,UAAU,CAACP,IAAI,CAAC;MACzB;IACF,CAAC;IACD,oBACE1I,OAAA;MAAMuG,QAAQ,EAAEyB,YAAa;MAACzD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACjDxE,OAAA;QAAKuE,SAAS,EAAC,0FAA0F;QAAAC,QAAA,EAAC;MAA8C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC9J5E,OAAA;QAAKuE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBxE,OAAA;UAAOuE,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnF5E,OAAA;UACE6E,IAAI,EAAC,MAAM;UACXqE,MAAM,EAAC,OAAO;UACdrC,QAAQ;UACR9B,QAAQ,EAAE0D,gBAAiB;UAC3BlE,SAAS,EAAC;QAAuL;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClM,CAAC,eACF5E,OAAA;UAAKuE,SAAS,EAAC,iDAAiD;UAAAC,QAAA,EAAEsD;QAAY;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC,eACN5E,OAAA;QAAKuE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CxE,OAAA;UAAQ6E,IAAI,EAAC,QAAQ;UAACK,OAAO,EAAEgC,QAAS;UAAC3C,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClI5E,OAAA;UAAQ6E,IAAI,EAAC,QAAQ;UAACN,SAAS,EAAC,gEAAgE;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX;EAEA,oBACE5E,OAAA;IAAMuG,QAAQ,EAAEyB,YAAa;IAACzD,SAAS,EAAC,WAAW;IAAAC,QAAA,GAChDyC,MAAM,aAANA,MAAM,wBAAAU,gBAAA,GAANV,MAAM,CAAEJ,QAAQ,cAAAc,gBAAA,uBAAhBA,gBAAA,CAAkBhC,GAAG,CAAEuC,KAAK,IAAKE,WAAW,CAACF,KAAK,EAAE,IAAI,CAAC,CAAC,EAC1DjB,MAAM,aAANA,MAAM,wBAAAW,gBAAA,GAANX,MAAM,CAAEkC,QAAQ,cAAAvB,gBAAA,uBAAhBA,gBAAA,CAAkBjC,GAAG,CAAEuC,KAAK,IAAKE,WAAW,CAACF,KAAK,EAAE,KAAK,CAAC,CAAC,eAC5DlI,OAAA;MAAKuE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CxE,OAAA;QAAQ6E,IAAI,EAAC,QAAQ;QAACK,OAAO,EAAEgC,QAAS;QAAC3C,SAAS,EAAC,+DAA+D;QAAAC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAClI5E,OAAA;QAAQ6E,IAAI,EAAC,QAAQ;QAACN,SAAS,EAAC,gEAAgE;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX;AAAC8C,GAAA,CA5FQV,cAAc;AAAAoC,GAAA,GAAdpC,cAAc;AA8FvB,eAAetG,GAAG;AAAC,IAAA8G,EAAA,EAAAC,GAAA,EAAA2B,GAAA;AAAAC,YAAA,CAAA7B,EAAA;AAAA6B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}