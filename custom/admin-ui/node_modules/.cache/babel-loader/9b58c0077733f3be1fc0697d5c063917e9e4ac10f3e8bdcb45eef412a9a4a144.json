{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/steampipe-compliance-unified/custom/admin-ui/src/App.js\",\n  _window$RUNTIME_CONFI,\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Users, UserPlus, Cloud, Trash2, KeyRound, X, Plus, Loader2 } from 'lucide-react';\nimport './index.css'; // Tailwind base\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = ((_window$RUNTIME_CONFI = window.RUNTIME_CONFIG) === null || _window$RUNTIME_CONFI === void 0 ? void 0 : _window$RUNTIME_CONFI.API_BASE_URL) || process.env.REACT_APP_API_BASE_URL || 'http://localhost:8084';\nfunction App() {\n  _s();\n  const [customers, setCustomers] = useState([]);\n  const [selectedCustomer, setSelectedCustomer] = useState(null);\n  const [showAddCustomer, setShowAddCustomer] = useState(false);\n  const [showAddCredentials, setShowAddCredentials] = useState(false);\n  const [selectedCloud, setSelectedCloud] = useState('');\n  const [credentialSchemas, setCredentialSchemas] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [useTestEndpoints, setUseTestEndpoints] = useState(true);\n  const [darkMode, setDarkMode] = useState(() => {\n    if (typeof window !== 'undefined') {\n      const saved = localStorage.getItem('darkMode');\n      return saved ? JSON.parse(saved) : false;\n    }\n    return false;\n  });\n  useEffect(() => {\n    fetchCustomers();\n    fetchCredentialSchemas();\n  }, []);\n  useEffect(() => {\n    if (darkMode) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n    localStorage.setItem('darkMode', JSON.stringify(darkMode));\n  }, [darkMode]);\n  const fetchCustomers = async () => {\n    try {\n      setLoading(true);\n      const endpoint = useTestEndpoints ? '/api/admin/test/customers' : '/api/admin/customers';\n      const response = await fetch(`${API_BASE_URL}${endpoint}`);\n      const data = await response.json();\n      if (data.status === 'success') {\n        setCustomers(data.customers);\n        // Auto-select the first customer if none is selected\n        if (data.customers.length > 0 && !selectedCustomer) {\n          setSelectedCustomer(data.customers[0]);\n        }\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to fetch customers: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchCredentialSchemas = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/api/admin/credential-schemas`);\n      const data = await response.json();\n      if (data.status === 'success') {\n        setCredentialSchemas(data.schemas);\n      }\n    } catch (err) {\n      // ignore\n    }\n  };\n  const handleAddCustomer = async customerData => {\n    try {\n      setLoading(true);\n      const endpoint = useTestEndpoints ? '/api/admin/test/customers' : '/api/admin/customers';\n      const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(customerData)\n      });\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n        setShowAddCustomer(false);\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to add customer: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteCustomer = async customerId => {\n    if (!window.confirm('Are you sure you want to delete this customer and all their credentials?')) return;\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/admin/customers/${customerId}`, {\n        method: 'DELETE'\n      });\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n        setSelectedCustomer(null);\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to delete customer');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAddCredentials = async credentialData => {\n    if (!selectedCustomer || !selectedCloud) return;\n    try {\n      setLoading(true);\n      const endpoint = useTestEndpoints ? `/api/admin/test/customers/${selectedCustomer.id}/credentials/${selectedCloud}` : `/api/admin/customers/${selectedCustomer.id}/credentials/${selectedCloud}`;\n      const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(credentialData)\n      });\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n        setShowAddCredentials(false);\n        setSelectedCloud('');\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to add credentials');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteCredentials = async (customerId, cloud) => {\n    if (!window.confirm(`Are you sure you want to delete ${cloud} credentials?`)) return;\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/admin/customers/${customerId}/credentials/${cloud}`, {\n        method: 'DELETE'\n      });\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to delete credentials');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Also, if the customer list changes (e.g. after add/delete), auto-select the first if none is selected\n  useEffect(() => {\n    if (customers.length > 0 && !selectedCustomer) {\n      setSelectedCustomer(customers[0]);\n    }\n  }, [customers, selectedCustomer]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 transition-colors duration-200\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-blue-700 text-white px-8 py-4 flex justify-between items-center shadow\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-semibold flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Users, {\n          className: \"w-7 h-7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), \"Steampipe Compliance Admin\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center gap-2 cursor-pointer text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: useTestEndpoints,\n            onChange: e => {\n              setUseTestEndpoints(e.target.checked);\n              fetchCustomers();\n            },\n            className: \"accent-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), \"Use Test Endpoints (No Auth)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setDarkMode(d => !d),\n          className: \"p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200\",\n          \"aria-label\": darkMode ? 'Switch to light mode' : 'Switch to dark mode',\n          children: darkMode ? /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 text-yellow-500\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: 2,\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M12 3v1m0 16v1m8.66-13.66l-.71.71M4.05 19.07l-.71.71M21 12h-1M4 12H3m16.66 5.66l-.71-.71M4.05 4.93l-.71-.71M12 5a7 7 0 100 14 7 7 0 000-14z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 126\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 text-gray-700\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: 2,\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M21 12.79A9 9 0 1111.21 3a7 7 0 109.79 9.79z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 124\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"max-w-5xl mx-auto py-8 px-4 flex flex-col min-h-[80vh] justify-center items-center\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-500 rounded-md flex items-start gap-3 animate-fadeIn\",\n        children: [/*#__PURE__*/_jsxDEV(X, {\n          className: \"w-5 h-5 mt-0.5 flex-shrink-0 cursor-pointer\",\n          onClick: () => setError(null)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-medium\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-4xl items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-8 transition-all duration-300 hover:shadow-3xl flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Users, {\n                className: \"w-5 h-5 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), \" Customers\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center gap-2 px-5 py-2 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-full shadow-lg hover:from-indigo-600 hover:to-blue-600 transition\",\n              onClick: () => setShowAddCustomer(true),\n              disabled: loading,\n              children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), \" Add Customer\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(Loader2, {\n              className: \"w-4 h-4 animate-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 80\n            }, this), \" Loading...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col gap-3 max-h-62 overflow-y-auto\",\n            children: customers.map(customer => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `border rounded-lg p-4 cursor-pointer transition-all duration-200 ${(selectedCustomer === null || selectedCustomer === void 0 ? void 0 : selectedCustomer.id) === customer.id ? 'border-blue-600 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700'}`,\n              onClick: () => setSelectedCustomer(customer),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                    children: customer.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                    children: [\"ID: \", customer.id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2\",\n                  children: ['aws', 'azure', 'gcp'].map(cloud => {\n                    var _customer$cloud_crede;\n                    return /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 py-1 rounded-full text-xs font-semibold ${(_customer$cloud_crede = customer.cloud_credentials[cloud]) !== null && _customer$cloud_crede !== void 0 && _customer$cloud_crede.configured ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'}`,\n                      children: cloud.toUpperCase()\n                    }, cloud, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 25\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)\n            }, customer.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), selectedCustomer && /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-8 transition-all duration-300 hover:shadow-3xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n                className: \"w-5 h-5 text-indigo-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this), \" \", selectedCustomer.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center gap-2 px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-all duration-200 shadow\",\n              onClick: () => handleDeleteCustomer(selectedCustomer.id),\n              children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), \" Delete Customer\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 dark:text-gray-400 mb-4\",\n            children: [\"ID: \", selectedCustomer.id]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(KeyRound, {\n                className: \"w-5 h-5 text-indigo-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), \" Cloud Credentials\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), ['aws', 'azure', 'gcp'].map(cloud => {\n              var _selectedCustomer$clo, _selectedCustomer$clo2, _selectedCustomer$clo3;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between border rounded-lg p-3 bg-gray-50 dark:bg-gray-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Cloud, {\n                    className: \"w-4 h-4 text-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-gray-900 dark:text-white\",\n                    children: cloud.toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `ml-2 text-xs font-semibold ${(_selectedCustomer$clo = selectedCustomer.cloud_credentials[cloud]) !== null && _selectedCustomer$clo !== void 0 && _selectedCustomer$clo.configured ? 'text-green-600' : 'text-red-600'}`,\n                    children: (_selectedCustomer$clo2 = selectedCustomer.cloud_credentials[cloud]) !== null && _selectedCustomer$clo2 !== void 0 && _selectedCustomer$clo2.configured ? '✓ Configured' : '✗ Not configured'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this), (_selectedCustomer$clo3 = selectedCustomer.cloud_credentials[cloud]) !== null && _selectedCustomer$clo3 !== void 0 && _selectedCustomer$clo3.configured ? /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"flex items-center gap-1 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 text-xs\",\n                  onClick: () => handleDeleteCredentials(selectedCustomer.id, cloud),\n                  children: [/*#__PURE__*/_jsxDEV(Trash2, {\n                    className: \"w-3 h-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 25\n                  }, this), \" Delete\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"flex items-center gap-1 px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 text-xs\",\n                  onClick: () => {\n                    setSelectedCloud(cloud);\n                    setShowAddCredentials(true);\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Plus, {\n                    className: \"w-3 h-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 25\n                  }, this), \" Add\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this)]\n              }, cloud, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this);\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), showAddCustomer && /*#__PURE__*/_jsxDEV(Modal, {\n        onClose: () => setShowAddCustomer(false),\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-4 flex items-center gap-2 text-gray-900 dark:text-white\",\n          children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n            className: \"w-5 h-5 text-indigo-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 106\n          }, this), \" Add New Customer\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: e => {\n            e.preventDefault();\n            const formData = new FormData(e.target);\n            handleAddCustomer({\n              name: formData.get('name'),\n              id: formData.get('id') || undefined\n            });\n          },\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200\",\n              children: \"Customer Name*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"name\",\n              required: true,\n              className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white text-gray-900 dark:bg-gray-900 dark:text-white dark:border-gray-700\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200\",\n              children: \"Customer ID (optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"id\",\n              placeholder: \"Auto-generated if empty\",\n              className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white text-gray-900 dark:bg-gray-900 dark:text-white dark:border-gray-700\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end gap-2 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowAddCustomer(false),\n              className: \"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700\",\n              children: \"Add Customer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 11\n      }, this), showAddCredentials && selectedCloud && /*#__PURE__*/_jsxDEV(Modal, {\n        onClose: () => setShowAddCredentials(false),\n        large: true,\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-4 flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(KeyRound, {\n            className: \"w-5 h-5 text-indigo-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 76\n          }, this), \" Add \", selectedCloud.toUpperCase(), \" Credentials\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CredentialForm, {\n          cloud: selectedCloud,\n          schema: credentialSchemas[selectedCloud],\n          onSubmit: handleAddCredentials,\n          onCancel: () => setShowAddCredentials(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"WnkD3WwrkREBffLXJOouy/dVc50=\");\n_c = App;\nfunction Modal({\n  children,\n  onClose,\n  large\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-full ${large ? 'w-[32rem]' : 'w-[24rem]'} relative animate-fadeIn`,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200\",\n        onClick: onClose,\n        children: /*#__PURE__*/_jsxDEV(X, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), children]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 363,\n    columnNumber: 5\n  }, this);\n}\n_c2 = Modal;\nfunction CredentialForm({\n  cloud,\n  schema,\n  onSubmit,\n  onCancel\n}) {\n  _s2();\n  var _schema$required, _schema$optional;\n  const [formData, setFormData] = useState({});\n  const [fileFeedback, setFileFeedback] = useState('');\n  const handleSubmit = e => {\n    e.preventDefault();\n    onSubmit(formData);\n  };\n  const handleChange = (field, value) => {\n    setFormData({\n      ...formData,\n      [field]: value\n    });\n  };\n  const renderField = (field, required) => {\n    if (cloud === 'gcp' && field === 'private_key') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium mb-1\",\n          children: [field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()), required && '*']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          name: field,\n          required: required,\n          rows: 8,\n          placeholder: \"-----BEGIN PRIVATE KEY-----\\\\n...\\\\n-----END PRIVATE KEY-----\",\n          className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n          onChange: e => handleChange(field, e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)]\n      }, field, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"block text-sm font-medium mb-1\",\n        children: [field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()), required && '*']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: field.includes('secret') || field.includes('password') ? 'password' : 'text',\n        name: field,\n        required: required,\n        className: \"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n        onChange: e => handleChange(field, e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this)]\n    }, field, true, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 7\n    }, this);\n  };\n  if (cloud === 'gcp') {\n    const handleFileUpload = e => {\n      const file = e.target.files[0];\n      if (file) {\n        const reader = new FileReader();\n        reader.onload = event => {\n          try {\n            const json = JSON.parse(event.target.result);\n            setFormData(json);\n            setFileFeedback(`File loaded: ${file.name}`);\n          } catch (err) {\n            setFileFeedback('Invalid JSON file');\n          }\n        };\n        reader.readAsText(file);\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-50 dark:bg-blue-900/20 p-3 rounded text-sm text-blue-700 dark:text-blue-200 mb-2\",\n        children: \"Upload your GCP service account JSON key file.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium mb-1\",\n          children: \"Service Account JSON File\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          accept: \".json\",\n          required: true,\n          onChange: handleFileUpload,\n          className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 text-xs text-green-600 dark:text-green-300\",\n          children: fileFeedback\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end gap-2 mt-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: onCancel,\n          className: \"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700\",\n          children: \"Save Credentials\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    className: \"space-y-4\",\n    children: [schema === null || schema === void 0 ? void 0 : (_schema$required = schema.required) === null || _schema$required === void 0 ? void 0 : _schema$required.map(field => renderField(field, true)), schema === null || schema === void 0 ? void 0 : (_schema$optional = schema.optional) === null || _schema$optional === void 0 ? void 0 : _schema$optional.map(field => renderField(field, false)), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end gap-2 mt-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: onCancel,\n        className: \"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700\",\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700\",\n        children: \"Save Credentials\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 457,\n    columnNumber: 5\n  }, this);\n}\n_s2(CredentialForm, \"9SsinILHtEsjwFFXZvHXapiNMd0=\");\n_c3 = CredentialForm;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"App\");\n$RefreshReg$(_c2, \"Modal\");\n$RefreshReg$(_c3, \"CredentialForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Users", "UserPlus", "Cloud", "Trash2", "KeyRound", "X", "Plus", "Loader2", "jsxDEV", "_jsxDEV", "API_BASE_URL", "_window$RUNTIME_CONFI", "window", "RUNTIME_CONFIG", "process", "env", "REACT_APP_API_BASE_URL", "App", "_s", "customers", "setCustomers", "selectedCustomer", "setSelectedCustomer", "showAddCustomer", "setShowAddCustomer", "showAddCredentials", "setShowAddCredentials", "selectedCloud", "setSelectedCloud", "credentialSchemas", "setCredentialSchemas", "loading", "setLoading", "error", "setError", "useTestEndpoints", "setUseTestEndpoints", "darkMode", "setDarkMode", "saved", "localStorage", "getItem", "JSON", "parse", "fetchCustomers", "fetchCredentialSchemas", "document", "documentElement", "classList", "add", "remove", "setItem", "stringify", "endpoint", "response", "fetch", "data", "json", "status", "length", "err", "message", "schemas", "handleAddCustomer", "customerData", "method", "headers", "body", "handleDeleteCustomer", "customerId", "confirm", "handleAddCredentials", "credentialData", "id", "handleDeleteCredentials", "cloud", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "checked", "onChange", "e", "target", "onClick", "d", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "disabled", "map", "customer", "name", "_customer$cloud_crede", "cloud_credentials", "configured", "toUpperCase", "_selectedCustomer$clo", "_selectedCustomer$clo2", "_selectedCustomer$clo3", "Modal", "onClose", "onSubmit", "preventDefault", "formData", "FormData", "get", "undefined", "required", "placeholder", "large", "CredentialForm", "schema", "onCancel", "_c", "_c2", "_s2", "_schema$required", "_schema$optional", "setFormData", "fileFeedback", "setFileFeedback", "handleSubmit", "handleChange", "field", "value", "renderField", "replace", "l", "rows", "includes", "handleFileUpload", "file", "files", "reader", "FileReader", "onload", "event", "result", "readAsText", "accept", "optional", "_c3", "$RefreshReg$"], "sources": ["/Users/<USER>/steampipe-compliance-unified/custom/admin-ui/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Users, UserPlus, Cloud, Trash2, KeyRound, X, Plus, Loader2 } from 'lucide-react';\nimport './index.css'; // Tailwind base\n\nconst API_BASE_URL = window.RUNTIME_CONFIG?.API_BASE_URL || \n                     process.env.REACT_APP_API_BASE_URL || \n                     'http://localhost:8084';\n\nfunction App() {\n  const [customers, setCustomers] = useState([]);\n  const [selectedCustomer, setSelectedCustomer] = useState(null);\n  const [showAddCustomer, setShowAddCustomer] = useState(false);\n  const [showAddCredentials, setShowAddCredentials] = useState(false);\n  const [selectedCloud, setSelectedCloud] = useState('');\n  const [credentialSchemas, setCredentialSchemas] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [useTestEndpoints, setUseTestEndpoints] = useState(true);\n  const [darkMode, setDarkMode] = useState(() => {\n    if (typeof window !== 'undefined') {\n      const saved = localStorage.getItem('darkMode');\n      return saved ? JSON.parse(saved) : false;\n    }\n    return false;\n  });\n\n  useEffect(() => {\n    fetchCustomers();\n    fetchCredentialSchemas();\n  }, []);\n\n  useEffect(() => {\n    if (darkMode) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n    localStorage.setItem('darkMode', JSON.stringify(darkMode));\n  }, [darkMode]);\n\n  const fetchCustomers = async () => {\n    try {\n      setLoading(true);\n      const endpoint = useTestEndpoints ? '/api/admin/test/customers' : '/api/admin/customers';\n      const response = await fetch(`${API_BASE_URL}${endpoint}`);\n      const data = await response.json();\n      if (data.status === 'success') {\n        setCustomers(data.customers);\n        // Auto-select the first customer if none is selected\n        if (data.customers.length > 0 && !selectedCustomer) {\n          setSelectedCustomer(data.customers[0]);\n        }\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to fetch customers: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchCredentialSchemas = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/api/admin/credential-schemas`);\n      const data = await response.json();\n      if (data.status === 'success') {\n        setCredentialSchemas(data.schemas);\n      }\n    } catch (err) {\n      // ignore\n    }\n  };\n\n  const handleAddCustomer = async (customerData) => {\n    try {\n      setLoading(true);\n      const endpoint = useTestEndpoints ? '/api/admin/test/customers' : '/api/admin/customers';\n      const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(customerData),\n      });\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n        setShowAddCustomer(false);\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to add customer: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteCustomer = async (customerId) => {\n    if (!window.confirm('Are you sure you want to delete this customer and all their credentials?')) return;\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/admin/customers/${customerId}`, { method: 'DELETE' });\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n        setSelectedCustomer(null);\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to delete customer');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAddCredentials = async (credentialData) => {\n    if (!selectedCustomer || !selectedCloud) return;\n    try {\n      setLoading(true);\n      const endpoint = useTestEndpoints \n        ? `/api/admin/test/customers/${selectedCustomer.id}/credentials/${selectedCloud}`\n        : `/api/admin/customers/${selectedCustomer.id}/credentials/${selectedCloud}`;\n      const response = await fetch(\n        `${API_BASE_URL}${endpoint}`,\n        {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify(credentialData),\n        }\n      );\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n        setShowAddCredentials(false);\n        setSelectedCloud('');\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to add credentials');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteCredentials = async (customerId, cloud) => {\n    if (!window.confirm(`Are you sure you want to delete ${cloud} credentials?`)) return;\n    try {\n      setLoading(true);\n      const response = await fetch(\n        `${API_BASE_URL}/api/admin/customers/${customerId}/credentials/${cloud}`,\n        { method: 'DELETE' }\n      );\n      const data = await response.json();\n      if (data.status === 'success') {\n        await fetchCustomers();\n      } else {\n        setError(data.error);\n      }\n    } catch (err) {\n      setError('Failed to delete credentials');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Also, if the customer list changes (e.g. after add/delete), auto-select the first if none is selected\n  useEffect(() => {\n    if (customers.length > 0 && !selectedCustomer) {\n      setSelectedCustomer(customers[0]);\n    }\n  }, [customers, selectedCustomer]);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 transition-colors duration-200\">\n      <header className=\"bg-blue-700 text-white px-8 py-4 flex justify-between items-center shadow\">\n        <h1 className=\"text-2xl font-semibold flex items-center gap-2\">\n          <Users className=\"w-7 h-7\" />\n          Steampipe Compliance Admin\n        </h1>\n        <div className=\"flex items-center gap-4\">\n          <label className=\"flex items-center gap-2 cursor-pointer text-sm\">\n            <input\n              type=\"checkbox\"\n              checked={useTestEndpoints}\n              onChange={(e) => {\n                setUseTestEndpoints(e.target.checked);\n                fetchCustomers();\n              }}\n              className=\"accent-blue-600\"\n            />\n            Use Test Endpoints (No Auth)\n          </label>\n          <button\n            onClick={() => setDarkMode((d) => !d)}\n            className=\"p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200\"\n            aria-label={darkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n          >\n            {darkMode ? (\n              <svg className=\"w-5 h-5 text-yellow-500\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2} viewBox=\"0 0 24 24\"><path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 3v1m0 16v1m8.66-13.66l-.71.71M4.05 19.07l-.71.71M21 12h-1M4 12H3m16.66 5.66l-.71-.71M4.05 4.93l-.71-.71M12 5a7 7 0 100 14 7 7 0 000-14z\" /></svg>\n            ) : (\n              <svg className=\"w-5 h-5 text-gray-700\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2} viewBox=\"0 0 24 24\"><path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M21 12.79A9 9 0 1111.21 3a7 7 0 109.79 9.79z\" /></svg>\n            )}\n          </button>\n        </div>\n      </header>\n\n      <main className=\"max-w-5xl mx-auto py-8 px-4 flex flex-col min-h-[80vh] justify-center items-center\">\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-500 rounded-md flex items-start gap-3 animate-fadeIn\">\n            <X className=\"w-5 h-5 mt-0.5 flex-shrink-0 cursor-pointer\" onClick={() => setError(null)} />\n            <div>\n              <p className=\"font-medium\">{error}</p>\n            </div>\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-4xl items-start\">\n          {/* Customers List */}\n          <section className=\"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-8 transition-all duration-300 hover:shadow-3xl flex flex-col\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h2 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2\">\n                <Users className=\"w-5 h-5 text-blue-600\" /> Customers\n              </h2>\n              <button\n                className=\"flex items-center gap-2 px-5 py-2 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-full shadow-lg hover:from-indigo-600 hover:to-blue-600 transition\"\n                onClick={() => setShowAddCustomer(true)}\n                disabled={loading}\n              >\n                <UserPlus className=\"w-4 h-4\" /> Add Customer\n              </button>\n            </div>\n            {loading && <div className=\"flex items-center gap-2 text-gray-500\"><Loader2 className=\"w-4 h-4 animate-spin\" /> Loading...</div>}\n            <div className=\"flex flex-col gap-3 max-h-62 overflow-y-auto\">\n              {customers.map((customer) => (\n                <div\n                  key={customer.id}\n                  className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${selectedCustomer?.id === customer.id ? 'border-blue-600 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700'}`}\n                  onClick={() => setSelectedCustomer(customer)}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">{customer.name}</h3>\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">ID: {customer.id}</p>\n                    </div>\n                    <div className=\"flex gap-2\">\n                      {['aws', 'azure', 'gcp'].map((cloud) => (\n                        <span\n                          key={cloud}\n                          className={`px-2 py-1 rounded-full text-xs font-semibold ${customer.cloud_credentials[cloud]?.configured ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'}`}\n                        >\n                          {cloud.toUpperCase()}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </section>\n\n          {/* Customer Details */}\n          {selectedCustomer && (\n            <section className=\"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700/60 p-8 transition-all duration-300 hover:shadow-3xl\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h2 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2\">\n                  <UserPlus className=\"w-5 h-5 text-indigo-600\" /> {selectedCustomer.name}\n                </h2>\n                <button\n                  className=\"flex items-center gap-2 px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-all duration-200 shadow\"\n                  onClick={() => handleDeleteCustomer(selectedCustomer.id)}\n                >\n                  <Trash2 className=\"w-4 h-4\" /> Delete Customer\n                </button>\n              </div>\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mb-4\">ID: {selectedCustomer.id}</p>\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2\">\n                  <KeyRound className=\"w-5 h-5 text-indigo-600\" /> Cloud Credentials\n                </h3>\n                {['aws', 'azure', 'gcp'].map((cloud) => (\n                  <div key={cloud} className=\"flex items-center justify-between border rounded-lg p-3 bg-gray-50 dark:bg-gray-700\">\n                    <div className=\"flex items-center gap-2\">\n                      <Cloud className=\"w-4 h-4 text-blue-500\" />\n                      <span className=\"font-medium text-gray-900 dark:text-white\">{cloud.toUpperCase()}</span>\n                      <span className={`ml-2 text-xs font-semibold ${selectedCustomer.cloud_credentials[cloud]?.configured ? 'text-green-600' : 'text-red-600'}`}>\n                        {selectedCustomer.cloud_credentials[cloud]?.configured ? '✓ Configured' : '✗ Not configured'}\n                      </span>\n                    </div>\n                    {selectedCustomer.cloud_credentials[cloud]?.configured ? (\n                      <button\n                        className=\"flex items-center gap-1 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 text-xs\"\n                        onClick={() => handleDeleteCredentials(selectedCustomer.id, cloud)}\n                      >\n                        <Trash2 className=\"w-3 h-3\" /> Delete\n                      </button>\n                    ) : (\n                      <button\n                        className=\"flex items-center gap-1 px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 text-xs\"\n                        onClick={() => { setSelectedCloud(cloud); setShowAddCredentials(true); }}\n                      >\n                        <Plus className=\"w-3 h-3\" /> Add\n                      </button>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </section>\n          )}\n        </div>\n\n        {/* Add Customer Modal */}\n        {showAddCustomer && (\n          <Modal onClose={() => setShowAddCustomer(false)}>\n            <h2 className=\"text-xl font-bold mb-4 flex items-center gap-2 text-gray-900 dark:text-white\"><UserPlus className=\"w-5 h-5 text-indigo-600\" /> Add New Customer</h2>\n            <form\n              onSubmit={(e) => {\n                e.preventDefault();\n                const formData = new FormData(e.target);\n                handleAddCustomer({\n                  name: formData.get('name'),\n                  id: formData.get('id') || undefined,\n                });\n              }}\n              className=\"space-y-4\"\n            >\n              <div>\n                <label className=\"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200\">Customer Name*</label>\n                <input type=\"text\" name=\"name\" required className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white text-gray-900 dark:bg-gray-900 dark:text-white dark:border-gray-700\" />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-200\">Customer ID (optional)</label>\n                <input type=\"text\" name=\"id\" placeholder=\"Auto-generated if empty\" className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white text-gray-900 dark:bg-gray-900 dark:text-white dark:border-gray-700\" />\n              </div>\n              <div className=\"flex justify-end gap-2 mt-6\">\n                <button type=\"button\" onClick={() => setShowAddCustomer(false)} className=\"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700\">Cancel</button>\n                <button type=\"submit\" className=\"px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700\">Add Customer</button>\n              </div>\n            </form>\n          </Modal>\n        )}\n\n        {/* Add Credentials Modal */}\n        {showAddCredentials && selectedCloud && (\n          <Modal onClose={() => setShowAddCredentials(false)} large>\n            <h2 className=\"text-xl font-bold mb-4 flex items-center gap-2\"><KeyRound className=\"w-5 h-5 text-indigo-600\" /> Add {selectedCloud.toUpperCase()} Credentials</h2>\n            <CredentialForm\n              cloud={selectedCloud}\n              schema={credentialSchemas[selectedCloud]}\n              onSubmit={handleAddCredentials}\n              onCancel={() => setShowAddCredentials(false)}\n            />\n          </Modal>\n        )}\n      </main>\n    </div>\n  );\n}\n\nfunction Modal({ children, onClose, large }) {\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\">\n      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-full ${large ? 'w-[32rem]' : 'w-[24rem]'} relative animate-fadeIn`}>\n        <button className=\"absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200\" onClick={onClose}>\n          <X className=\"w-5 h-5\" />\n        </button>\n        {children}\n      </div>\n    </div>\n  );\n}\n\nfunction CredentialForm({ cloud, schema, onSubmit, onCancel }) {\n  const [formData, setFormData] = useState({});\n  const [fileFeedback, setFileFeedback] = useState('');\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onSubmit(formData);\n  };\n\n  const handleChange = (field, value) => {\n    setFormData({ ...formData, [field]: value });\n  };\n\n  const renderField = (field, required) => {\n    if (cloud === 'gcp' && field === 'private_key') {\n      return (\n        <div key={field} className=\"mb-4\">\n          <label className=\"block text-sm font-medium mb-1\">{field.replace(/_/g, ' ').replace(/\\b\\w/g, (l) => l.toUpperCase())}{required && '*'}</label>\n          <textarea\n            name={field}\n            required={required}\n            rows={8}\n            placeholder=\"-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\"\n            className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n            onChange={(e) => handleChange(field, e.target.value)}\n          />\n        </div>\n      );\n    }\n    return (\n      <div key={field} className=\"mb-4\">\n        <label className=\"block text-sm font-medium mb-1\">{field.replace(/_/g, ' ').replace(/\\b\\w/g, (l) => l.toUpperCase())}{required && '*'}</label>\n        <input\n          type={field.includes('secret') || field.includes('password') ? 'password' : 'text'}\n          name={field}\n          required={required}\n          className=\"w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n          onChange={(e) => handleChange(field, e.target.value)}\n        />\n      </div>\n    );\n  };\n\n  if (cloud === 'gcp') {\n    const handleFileUpload = (e) => {\n      const file = e.target.files[0];\n      if (file) {\n        const reader = new FileReader();\n        reader.onload = (event) => {\n          try {\n            const json = JSON.parse(event.target.result);\n            setFormData(json);\n            setFileFeedback(`File loaded: ${file.name}`);\n          } catch (err) {\n            setFileFeedback('Invalid JSON file');\n          }\n        };\n        reader.readAsText(file);\n      }\n    };\n    return (\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 p-3 rounded text-sm text-blue-700 dark:text-blue-200 mb-2\">Upload your GCP service account JSON key file.</div>\n        <div className=\"mb-4\">\n          <label className=\"block text-sm font-medium mb-1\">Service Account JSON File</label>\n          <input\n            type=\"file\"\n            accept=\".json\"\n            required\n            onChange={handleFileUpload}\n            className=\"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\"\n          />\n          <div className=\"mt-2 text-xs text-green-600 dark:text-green-300\">{fileFeedback}</div>\n        </div>\n        <div className=\"flex justify-end gap-2 mt-6\">\n          <button type=\"button\" onClick={onCancel} className=\"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700\">Cancel</button>\n          <button type=\"submit\" className=\"px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700\">Save Credentials</button>\n        </div>\n      </form>\n    );\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\n      {schema?.required?.map((field) => renderField(field, true))}\n      {schema?.optional?.map((field) => renderField(field, false))}\n      <div className=\"flex justify-end gap-2 mt-6\">\n        <button type=\"button\" onClick={onCancel} className=\"px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700\">Cancel</button>\n        <button type=\"submit\" className=\"px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700\">Save Credentials</button>\n      </div>\n    </form>\n  );\n}\n\nexport default App;"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,CAAC,EAAEC,IAAI,EAAEC,OAAO,QAAQ,cAAc;AACzF,OAAO,aAAa,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,YAAY,GAAG,EAAAC,qBAAA,GAAAC,MAAM,CAACC,cAAc,cAAAF,qBAAA,uBAArBA,qBAAA,CAAuBD,YAAY,KACnCI,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAClC,uBAAuB;AAE5C,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,MAAM;IAC7C,IAAI,OAAOc,MAAM,KAAK,WAAW,EAAE;MACjC,MAAM2B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MAC9C,OAAOF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,KAAK;IAC1C;IACA,OAAO,KAAK;EACd,CAAC,CAAC;EAEFxC,SAAS,CAAC,MAAM;IACd6C,cAAc,CAAC,CAAC;IAChBC,sBAAsB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN9C,SAAS,CAAC,MAAM;IACd,IAAIsC,QAAQ,EAAE;MACZS,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;IAChD,CAAC,MAAM;MACLH,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACE,MAAM,CAAC,MAAM,CAAC;IACnD;IACAV,YAAY,CAACW,OAAO,CAAC,UAAU,EAAET,IAAI,CAACU,SAAS,CAACf,QAAQ,CAAC,CAAC;EAC5D,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,MAAMO,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqB,QAAQ,GAAGlB,gBAAgB,GAAG,2BAA2B,GAAG,sBAAsB;MACxF,MAAMmB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG7C,YAAY,GAAG2C,QAAQ,EAAE,CAAC;MAC1D,MAAMG,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,MAAM,KAAK,SAAS,EAAE;QAC7BtC,YAAY,CAACoC,IAAI,CAACrC,SAAS,CAAC;QAC5B;QACA,IAAIqC,IAAI,CAACrC,SAAS,CAACwC,MAAM,GAAG,CAAC,IAAI,CAACtC,gBAAgB,EAAE;UAClDC,mBAAmB,CAACkC,IAAI,CAACrC,SAAS,CAAC,CAAC,CAAC,CAAC;QACxC;MACF,CAAC,MAAM;QACLe,QAAQ,CAACsB,IAAI,CAACvB,KAAK,CAAC;MACtB;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ1B,QAAQ,CAAC,6BAA6B,GAAG0B,GAAG,CAACC,OAAO,CAAC;IACvD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG7C,YAAY,+BAA+B,CAAC;MAC5E,MAAM8C,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,MAAM,KAAK,SAAS,EAAE;QAC7B5B,oBAAoB,CAAC0B,IAAI,CAACM,OAAO,CAAC;MACpC;IACF,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZ;IAAA;EAEJ,CAAC;EAED,MAAMG,iBAAiB,GAAG,MAAOC,YAAY,IAAK;IAChD,IAAI;MACFhC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqB,QAAQ,GAAGlB,gBAAgB,GAAG,2BAA2B,GAAG,sBAAsB;MACxF,MAAMmB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG7C,YAAY,GAAG2C,QAAQ,EAAE,EAAE;QACzDY,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEzB,IAAI,CAACU,SAAS,CAACY,YAAY;MACnC,CAAC,CAAC;MACF,MAAMR,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,MAAM,KAAK,SAAS,EAAE;QAC7B,MAAMd,cAAc,CAAC,CAAC;QACtBpB,kBAAkB,CAAC,KAAK,CAAC;MAC3B,CAAC,MAAM;QACLU,QAAQ,CAACsB,IAAI,CAACvB,KAAK,CAAC;MACtB;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ1B,QAAQ,CAAC,0BAA0B,GAAG0B,GAAG,CAACC,OAAO,CAAC;IACpD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,oBAAoB,GAAG,MAAOC,UAAU,IAAK;IACjD,IAAI,CAACzD,MAAM,CAAC0D,OAAO,CAAC,0EAA0E,CAAC,EAAE;IACjG,IAAI;MACFtC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG7C,YAAY,wBAAwB2D,UAAU,EAAE,EAAE;QAAEJ,MAAM,EAAE;MAAS,CAAC,CAAC;MACvG,MAAMT,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,MAAM,KAAK,SAAS,EAAE;QAC7B,MAAMd,cAAc,CAAC,CAAC;QACtBtB,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLY,QAAQ,CAACsB,IAAI,CAACvB,KAAK,CAAC;MACtB;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ1B,QAAQ,CAAC,2BAA2B,CAAC;IACvC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuC,oBAAoB,GAAG,MAAOC,cAAc,IAAK;IACrD,IAAI,CAACnD,gBAAgB,IAAI,CAACM,aAAa,EAAE;IACzC,IAAI;MACFK,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqB,QAAQ,GAAGlB,gBAAgB,GAC7B,6BAA6Bd,gBAAgB,CAACoD,EAAE,gBAAgB9C,aAAa,EAAE,GAC/E,wBAAwBN,gBAAgB,CAACoD,EAAE,gBAAgB9C,aAAa,EAAE;MAC9E,MAAM2B,QAAQ,GAAG,MAAMC,KAAK,CAC1B,GAAG7C,YAAY,GAAG2C,QAAQ,EAAE,EAC5B;QACEY,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEzB,IAAI,CAACU,SAAS,CAACoB,cAAc;MACrC,CACF,CAAC;MACD,MAAMhB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,MAAM,KAAK,SAAS,EAAE;QAC7B,MAAMd,cAAc,CAAC,CAAC;QACtBlB,qBAAqB,CAAC,KAAK,CAAC;QAC5BE,gBAAgB,CAAC,EAAE,CAAC;MACtB,CAAC,MAAM;QACLM,QAAQ,CAACsB,IAAI,CAACvB,KAAK,CAAC;MACtB;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ1B,QAAQ,CAAC,2BAA2B,CAAC;IACvC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0C,uBAAuB,GAAG,MAAAA,CAAOL,UAAU,EAAEM,KAAK,KAAK;IAC3D,IAAI,CAAC/D,MAAM,CAAC0D,OAAO,CAAC,mCAAmCK,KAAK,eAAe,CAAC,EAAE;IAC9E,IAAI;MACF3C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsB,QAAQ,GAAG,MAAMC,KAAK,CAC1B,GAAG7C,YAAY,wBAAwB2D,UAAU,gBAAgBM,KAAK,EAAE,EACxE;QAAEV,MAAM,EAAE;MAAS,CACrB,CAAC;MACD,MAAMT,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,MAAM,KAAK,SAAS,EAAE;QAC7B,MAAMd,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLV,QAAQ,CAACsB,IAAI,CAACvB,KAAK,CAAC;MACtB;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ1B,QAAQ,CAAC,8BAA8B,CAAC;IAC1C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAjC,SAAS,CAAC,MAAM;IACd,IAAIoB,SAAS,CAACwC,MAAM,GAAG,CAAC,IAAI,CAACtC,gBAAgB,EAAE;MAC7CC,mBAAmB,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACA,SAAS,EAAEE,gBAAgB,CAAC,CAAC;EAEjC,oBACEZ,OAAA;IAAKmE,SAAS,EAAC,wGAAwG;IAAAC,QAAA,gBACrHpE,OAAA;MAAQmE,SAAS,EAAC,2EAA2E;MAAAC,QAAA,gBAC3FpE,OAAA;QAAImE,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC5DpE,OAAA,CAACT,KAAK;UAAC4E,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,8BAE/B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLxE,OAAA;QAAKmE,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCpE,OAAA;UAAOmE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC/DpE,OAAA;YACEyE,IAAI,EAAC,UAAU;YACfC,OAAO,EAAEhD,gBAAiB;YAC1BiD,QAAQ,EAAGC,CAAC,IAAK;cACfjD,mBAAmB,CAACiD,CAAC,CAACC,MAAM,CAACH,OAAO,CAAC;cACrCvC,cAAc,CAAC,CAAC;YAClB,CAAE;YACFgC,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,gCAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRxE,OAAA;UACE8E,OAAO,EAAEA,CAAA,KAAMjD,WAAW,CAAEkD,CAAC,IAAK,CAACA,CAAC,CAAE;UACtCZ,SAAS,EAAC,qHAAqH;UAC/H,cAAYvC,QAAQ,GAAG,sBAAsB,GAAG,qBAAsB;UAAAwC,QAAA,EAErExC,QAAQ,gBACP5B,OAAA;YAAKmE,SAAS,EAAC,yBAAyB;YAACa,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAE,CAAE;YAACC,OAAO,EAAC,WAAW;YAAAf,QAAA,eAACpE,OAAA;cAAMoF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACN,CAAC,EAAC;YAA6I;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAE1TxE,OAAA;YAAKmE,SAAS,EAAC,uBAAuB;YAACa,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAE,CAAE;YAACC,OAAO,EAAC,WAAW;YAAAf,QAAA,eAACpE,OAAA;cAAMoF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACN,CAAC,EAAC;YAA8C;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACzN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETxE,OAAA;MAAMmE,SAAS,EAAC,oFAAoF;MAAAC,QAAA,GACjG5C,KAAK,iBACJxB,OAAA;QAAKmE,SAAS,EAAC,qGAAqG;QAAAC,QAAA,gBAClHpE,OAAA,CAACJ,CAAC;UAACuE,SAAS,EAAC,6CAA6C;UAACW,OAAO,EAAEA,CAAA,KAAMrD,QAAQ,CAAC,IAAI;QAAE;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5FxE,OAAA;UAAAoE,QAAA,eACEpE,OAAA;YAAGmE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE5C;UAAK;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDxE,OAAA;QAAKmE,SAAS,EAAC,oEAAoE;QAAAC,QAAA,gBAEjFpE,OAAA;UAASmE,SAAS,EAAC,gKAAgK;UAAAC,QAAA,gBACjLpE,OAAA;YAAKmE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDpE,OAAA;cAAImE,SAAS,EAAC,yEAAyE;cAAAC,QAAA,gBACrFpE,OAAA,CAACT,KAAK;gBAAC4E,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,cAC7C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxE,OAAA;cACEmE,SAAS,EAAC,qKAAqK;cAC/KW,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,IAAI,CAAE;cACxCuE,QAAQ,EAAEhE,OAAQ;cAAA8C,QAAA,gBAElBpE,OAAA,CAACR,QAAQ;gBAAC2E,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAClC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACLlD,OAAO,iBAAItB,OAAA;YAAKmE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAACpE,OAAA,CAACF,OAAO;cAACqE,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAAW;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChIxE,OAAA;YAAKmE,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAC1D1D,SAAS,CAAC6E,GAAG,CAAEC,QAAQ,iBACtBxF,OAAA;cAEEmE,SAAS,EAAE,oEAAoE,CAAAvD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoD,EAAE,MAAKwB,QAAQ,CAACxB,EAAE,GAAG,gDAAgD,GAAG,kEAAkE,EAAG;cAC9Oc,OAAO,EAAEA,CAAA,KAAMjE,mBAAmB,CAAC2E,QAAQ,CAAE;cAAApB,QAAA,eAE7CpE,OAAA;gBAAKmE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDpE,OAAA;kBAAAoE,QAAA,gBACEpE,OAAA;oBAAImE,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAAEoB,QAAQ,CAACC;kBAAI;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxFxE,OAAA;oBAAGmE,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,GAAC,MAAI,EAACoB,QAAQ,CAACxB,EAAE;kBAAA;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACNxE,OAAA;kBAAKmE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EACxB,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAACmB,GAAG,CAAErB,KAAK;oBAAA,IAAAwB,qBAAA;oBAAA,oBACjC1F,OAAA;sBAEEmE,SAAS,EAAE,gDAAgD,CAAAuB,qBAAA,GAAAF,QAAQ,CAACG,iBAAiB,CAACzB,KAAK,CAAC,cAAAwB,qBAAA,eAAjCA,qBAAA,CAAmCE,UAAU,GAAG,sEAAsE,GAAG,8DAA8D,EAAG;sBAAAxB,QAAA,EAEpPF,KAAK,CAAC2B,WAAW,CAAC;oBAAC,GAHf3B,KAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAIN,CAAC;kBAAA,CACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAnBDgB,QAAQ,CAACxB,EAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGT5D,gBAAgB,iBACfZ,OAAA;UAASmE,SAAS,EAAC,kJAAkJ;UAAAC,QAAA,gBACnKpE,OAAA;YAAKmE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDpE,OAAA;cAAImE,SAAS,EAAC,yEAAyE;cAAAC,QAAA,gBACrFpE,OAAA,CAACR,QAAQ;gBAAC2E,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAAC,EAAC5D,gBAAgB,CAAC6E,IAAI;YAAA;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACLxE,OAAA;cACEmE,SAAS,EAAC,wHAAwH;cAClIW,OAAO,EAAEA,CAAA,KAAMnB,oBAAoB,CAAC/C,gBAAgB,CAACoD,EAAE,CAAE;cAAAI,QAAA,gBAEzDpE,OAAA,CAACN,MAAM;gBAACyE,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAChC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNxE,OAAA;YAAGmE,SAAS,EAAC,+CAA+C;YAAAC,QAAA,GAAC,MAAI,EAACxD,gBAAgB,CAACoD,EAAE;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1FxE,OAAA;YAAKmE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBpE,OAAA;cAAImE,SAAS,EAAC,6EAA6E;cAAAC,QAAA,gBACzFpE,OAAA,CAACL,QAAQ;gBAACwE,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAClD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACJ,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAACe,GAAG,CAAErB,KAAK;cAAA,IAAA4B,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;cAAA,oBACjChG,OAAA;gBAAiBmE,SAAS,EAAC,qFAAqF;gBAAAC,QAAA,gBAC9GpE,OAAA;kBAAKmE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCpE,OAAA,CAACP,KAAK;oBAAC0E,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC3CxE,OAAA;oBAAMmE,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAEF,KAAK,CAAC2B,WAAW,CAAC;kBAAC;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxFxE,OAAA;oBAAMmE,SAAS,EAAE,8BAA8B,CAAA2B,qBAAA,GAAAlF,gBAAgB,CAAC+E,iBAAiB,CAACzB,KAAK,CAAC,cAAA4B,qBAAA,eAAzCA,qBAAA,CAA2CF,UAAU,GAAG,gBAAgB,GAAG,cAAc,EAAG;oBAAAxB,QAAA,EACxI,CAAA2B,sBAAA,GAAAnF,gBAAgB,CAAC+E,iBAAiB,CAACzB,KAAK,CAAC,cAAA6B,sBAAA,eAAzCA,sBAAA,CAA2CH,UAAU,GAAG,cAAc,GAAG;kBAAkB;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACL,CAAAwB,sBAAA,GAAApF,gBAAgB,CAAC+E,iBAAiB,CAACzB,KAAK,CAAC,cAAA8B,sBAAA,eAAzCA,sBAAA,CAA2CJ,UAAU,gBACpD5F,OAAA;kBACEmE,SAAS,EAAC,4FAA4F;kBACtGW,OAAO,EAAEA,CAAA,KAAMb,uBAAuB,CAACrD,gBAAgB,CAACoD,EAAE,EAAEE,KAAK,CAAE;kBAAAE,QAAA,gBAEnEpE,OAAA,CAACN,MAAM;oBAACyE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAChC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,gBAETxE,OAAA;kBACEmE,SAAS,EAAC,kGAAkG;kBAC5GW,OAAO,EAAEA,CAAA,KAAM;oBAAE3D,gBAAgB,CAAC+C,KAAK,CAAC;oBAAEjD,qBAAqB,CAAC,IAAI,CAAC;kBAAE,CAAE;kBAAAmD,QAAA,gBAEzEpE,OAAA,CAACH,IAAI;oBAACsE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,QAC9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA,GAtBON,KAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBV,CAAC;YAAA,CACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL1D,eAAe,iBACdd,OAAA,CAACiG,KAAK;QAACC,OAAO,EAAEA,CAAA,KAAMnF,kBAAkB,CAAC,KAAK,CAAE;QAAAqD,QAAA,gBAC9CpE,OAAA;UAAImE,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAACpE,OAAA,CAACR,QAAQ;YAAC2E,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAAiB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnKxE,OAAA;UACEmG,QAAQ,EAAGvB,CAAC,IAAK;YACfA,CAAC,CAACwB,cAAc,CAAC,CAAC;YAClB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC1B,CAAC,CAACC,MAAM,CAAC;YACvCvB,iBAAiB,CAAC;cAChBmC,IAAI,EAAEY,QAAQ,CAACE,GAAG,CAAC,MAAM,CAAC;cAC1BvC,EAAE,EAAEqC,QAAQ,CAACE,GAAG,CAAC,IAAI,CAAC,IAAIC;YAC5B,CAAC,CAAC;UACJ,CAAE;UACFrC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAErBpE,OAAA;YAAAoE,QAAA,gBACEpE,OAAA;cAAOmE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzGxE,OAAA;cAAOyE,IAAI,EAAC,MAAM;cAACgB,IAAI,EAAC,MAAM;cAACgB,QAAQ;cAACtC,SAAS,EAAC;YAAiL;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnO,CAAC,eACNxE,OAAA;YAAAoE,QAAA,gBACEpE,OAAA;cAAOmE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjHxE,OAAA;cAAOyE,IAAI,EAAC,MAAM;cAACgB,IAAI,EAAC,IAAI;cAACiB,WAAW,EAAC,yBAAyB;cAACvC,SAAS,EAAC;YAAiL;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9P,CAAC,eACNxE,OAAA;YAAKmE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CpE,OAAA;cAAQyE,IAAI,EAAC,QAAQ;cAACK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,KAAK,CAAE;cAACoD,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzJxE,OAAA;cAAQyE,IAAI,EAAC,QAAQ;cAACN,SAAS,EAAC,gEAAgE;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACR,EAGAxD,kBAAkB,IAAIE,aAAa,iBAClClB,OAAA,CAACiG,KAAK;QAACC,OAAO,EAAEA,CAAA,KAAMjF,qBAAqB,CAAC,KAAK,CAAE;QAAC0F,KAAK;QAAAvC,QAAA,gBACvDpE,OAAA;UAAImE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAACpE,OAAA,CAACL,QAAQ;YAACwE,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAAK,EAACtD,aAAa,CAAC2E,WAAW,CAAC,CAAC,EAAC,cAAY;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClKxE,OAAA,CAAC4G,cAAc;UACb1C,KAAK,EAAEhD,aAAc;UACrB2F,MAAM,EAAEzF,iBAAiB,CAACF,aAAa,CAAE;UACzCiF,QAAQ,EAAErC,oBAAqB;UAC/BgD,QAAQ,EAAEA,CAAA,KAAM7F,qBAAqB,CAAC,KAAK;QAAE;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC/D,EAAA,CA9VQD,GAAG;AAAAuG,EAAA,GAAHvG,GAAG;AAgWZ,SAASyF,KAAKA,CAAC;EAAE7B,QAAQ;EAAE8B,OAAO;EAAES;AAAM,CAAC,EAAE;EAC3C,oBACE3G,OAAA;IAAKmE,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFpE,OAAA;MAAKmE,SAAS,EAAE,iEAAiEwC,KAAK,GAAG,WAAW,GAAG,WAAW,0BAA2B;MAAAvC,QAAA,gBAC3IpE,OAAA;QAAQmE,SAAS,EAAC,mFAAmF;QAACW,OAAO,EAAEoB,OAAQ;QAAA9B,QAAA,eACrHpE,OAAA,CAACJ,CAAC;UAACuE,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,EACRJ,QAAQ;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACwC,GAAA,GAXQf,KAAK;AAad,SAASW,cAAcA,CAAC;EAAE1C,KAAK;EAAE2C,MAAM;EAAEV,QAAQ;EAAEW;AAAS,CAAC,EAAE;EAAAG,GAAA;EAAA,IAAAC,gBAAA,EAAAC,gBAAA;EAC7D,MAAM,CAACd,QAAQ,EAAEe,WAAW,CAAC,GAAG/H,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACgI,YAAY,EAAEC,eAAe,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAMkI,YAAY,GAAI3C,CAAC,IAAK;IAC1BA,CAAC,CAACwB,cAAc,CAAC,CAAC;IAClBD,QAAQ,CAACE,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMmB,YAAY,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrCN,WAAW,CAAC;MAAE,GAAGf,QAAQ;MAAE,CAACoB,KAAK,GAAGC;IAAM,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMC,WAAW,GAAGA,CAACF,KAAK,EAAEhB,QAAQ,KAAK;IACvC,IAAIvC,KAAK,KAAK,KAAK,IAAIuD,KAAK,KAAK,aAAa,EAAE;MAC9C,oBACEzH,OAAA;QAAiBmE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBAC/BpE,OAAA;UAAOmE,SAAS,EAAC,gCAAgC;UAAAC,QAAA,GAAEqD,KAAK,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAAChC,WAAW,CAAC,CAAC,CAAC,EAAEY,QAAQ,IAAI,GAAG;QAAA;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9IxE,OAAA;UACEyF,IAAI,EAAEgC,KAAM;UACZhB,QAAQ,EAAEA,QAAS;UACnBqB,IAAI,EAAE,CAAE;UACRpB,WAAW,EAAC,+DAA6D;UACzEvC,SAAS,EAAC,oGAAoG;UAC9GQ,QAAQ,EAAGC,CAAC,IAAK4C,YAAY,CAACC,KAAK,EAAE7C,CAAC,CAACC,MAAM,CAAC6C,KAAK;QAAE;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA,GATMiD,KAAK;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUV,CAAC;IAEV;IACA,oBACExE,OAAA;MAAiBmE,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAC/BpE,OAAA;QAAOmE,SAAS,EAAC,gCAAgC;QAAAC,QAAA,GAAEqD,KAAK,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAAChC,WAAW,CAAC,CAAC,CAAC,EAAEY,QAAQ,IAAI,GAAG;MAAA;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC9IxE,OAAA;QACEyE,IAAI,EAAEgD,KAAK,CAACM,QAAQ,CAAC,QAAQ,CAAC,IAAIN,KAAK,CAACM,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,MAAO;QACnFtC,IAAI,EAAEgC,KAAM;QACZhB,QAAQ,EAAEA,QAAS;QACnBtC,SAAS,EAAC,oGAAoG;QAC9GQ,QAAQ,EAAGC,CAAC,IAAK4C,YAAY,CAACC,KAAK,EAAE7C,CAAC,CAACC,MAAM,CAAC6C,KAAK;MAAE;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA,GARMiD,KAAK;MAAApD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OASV,CAAC;EAEV,CAAC;EAED,IAAIN,KAAK,KAAK,KAAK,EAAE;IACnB,MAAM8D,gBAAgB,GAAIpD,CAAC,IAAK;MAC9B,MAAMqD,IAAI,GAAGrD,CAAC,CAACC,MAAM,CAACqD,KAAK,CAAC,CAAC,CAAC;MAC9B,IAAID,IAAI,EAAE;QACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAIC,KAAK,IAAK;UACzB,IAAI;YACF,MAAMtF,IAAI,GAAGf,IAAI,CAACC,KAAK,CAACoG,KAAK,CAACzD,MAAM,CAAC0D,MAAM,CAAC;YAC5CnB,WAAW,CAACpE,IAAI,CAAC;YACjBsE,eAAe,CAAC,gBAAgBW,IAAI,CAACxC,IAAI,EAAE,CAAC;UAC9C,CAAC,CAAC,OAAOtC,GAAG,EAAE;YACZmE,eAAe,CAAC,mBAAmB,CAAC;UACtC;QACF,CAAC;QACDa,MAAM,CAACK,UAAU,CAACP,IAAI,CAAC;MACzB;IACF,CAAC;IACD,oBACEjI,OAAA;MAAMmG,QAAQ,EAAEoB,YAAa;MAACpD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACjDpE,OAAA;QAAKmE,SAAS,EAAC,0FAA0F;QAAAC,QAAA,EAAC;MAA8C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC9JxE,OAAA;QAAKmE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBpE,OAAA;UAAOmE,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnFxE,OAAA;UACEyE,IAAI,EAAC,MAAM;UACXgE,MAAM,EAAC,OAAO;UACdhC,QAAQ;UACR9B,QAAQ,EAAEqD,gBAAiB;UAC3B7D,SAAS,EAAC;QAAuL;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClM,CAAC,eACFxE,OAAA;UAAKmE,SAAS,EAAC,iDAAiD;UAAAC,QAAA,EAAEiD;QAAY;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC,eACNxE,OAAA;QAAKmE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CpE,OAAA;UAAQyE,IAAI,EAAC,QAAQ;UAACK,OAAO,EAAEgC,QAAS;UAAC3C,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClIxE,OAAA;UAAQyE,IAAI,EAAC,QAAQ;UAACN,SAAS,EAAC,gEAAgE;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX;EAEA,oBACExE,OAAA;IAAMmG,QAAQ,EAAEoB,YAAa;IAACpD,SAAS,EAAC,WAAW;IAAAC,QAAA,GAChDyC,MAAM,aAANA,MAAM,wBAAAK,gBAAA,GAANL,MAAM,CAAEJ,QAAQ,cAAAS,gBAAA,uBAAhBA,gBAAA,CAAkB3B,GAAG,CAAEkC,KAAK,IAAKE,WAAW,CAACF,KAAK,EAAE,IAAI,CAAC,CAAC,EAC1DZ,MAAM,aAANA,MAAM,wBAAAM,gBAAA,GAANN,MAAM,CAAE6B,QAAQ,cAAAvB,gBAAA,uBAAhBA,gBAAA,CAAkB5B,GAAG,CAAEkC,KAAK,IAAKE,WAAW,CAACF,KAAK,EAAE,KAAK,CAAC,CAAC,eAC5DzH,OAAA;MAAKmE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CpE,OAAA;QAAQyE,IAAI,EAAC,QAAQ;QAACK,OAAO,EAAEgC,QAAS;QAAC3C,SAAS,EAAC,+DAA+D;QAAAC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAClIxE,OAAA;QAAQyE,IAAI,EAAC,QAAQ;QAACN,SAAS,EAAC,gEAAgE;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX;AAACyC,GAAA,CA5FQL,cAAc;AAAA+B,GAAA,GAAd/B,cAAc;AA8FvB,eAAepG,GAAG;AAAC,IAAAuG,EAAA,EAAAC,GAAA,EAAA2B,GAAA;AAAAC,YAAA,CAAA7B,EAAA;AAAA6B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}