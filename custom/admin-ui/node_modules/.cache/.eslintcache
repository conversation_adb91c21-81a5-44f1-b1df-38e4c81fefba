[{"/Users/<USER>/steampipe-compliance-unified/custom/admin-ui/src/index.js": "1", "/Users/<USER>/steampipe-compliance-unified/custom/admin-ui/src/App.js": "2"}, {"size": 253, "mtime": 1753189257500, "results": "3", "hashOfConfig": "4"}, {"size": 25891, "mtime": 1753284754300, "results": "5", "hashOfConfig": "6"}, {"filePath": "7", "messages": "8", "suppressedMessages": "9", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "p64zc2", {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "vvw93f", "/Users/<USER>/steampipe-compliance-unified/custom/admin-ui/src/index.js", [], [], "/Users/<USER>/steampipe-compliance-unified/custom/admin-ui/src/App.js", ["13"], [], {"ruleId": "14", "severity": 1, "message": "15", "line": 31, "column": 6, "nodeType": "16", "endLine": 31, "endColumn": 8, "suggestions": "17"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCustomers'. Either include it or remove the dependency array.", "ArrayExpression", ["18"], {"desc": "19", "fix": "20"}, "Update the dependencies array to be: [fetchCustomers]", {"range": "21", "text": "22"}, [1289, 1291], "[fetchCustomers]"]