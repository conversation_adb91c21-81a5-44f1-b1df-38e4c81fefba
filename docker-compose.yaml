version: '3.8'

services:
  # Base image service
  steampipe-base:
    build:
      context: .
      dockerfile: Base_image_steampipe/Dockerfile
    image: steampipe-base:latest
    command: echo "Base image built"

  # Admin API Service
  admin-api:
    build:
      context: .
      dockerfile: custom/services/admin/Dockerfile
    container_name: admin-api
    ports:
      - "3000:3000"
    networks:
      - app-network

  # GCP Compliance Service
  steampipe-gcp:
    build:
      context: .
      dockerfile: custom/services/gcp/Dockerfile
      args:
        - BASE_IMAGE=steampipe-base:latest
    container_name: steampipe-gcp-compliance
    depends_on:
      - steampipe-base
    volumes:
      - ./custom/services/gcp:/app/gcp
      - ./custom/shared:/app/shared
      - ./opensource/mods/gcp-compliance:/app/steampipe-mod-gcp-compliance
      - gcp-steampipe-internal:/home/<USER>/.steampipe/internal
      - ./custom/services/gcp/config/gcp.spc:/home/<USER>/.steampipe/config/gcp.spc
      - gcp-steampipe-logs:/home/<USER>/.steampipe/logs
      - gcp-steampipe-config:/home/<USER>/.steampipe/config
      - gcp-powerpipe-config:/home/<USER>/.powerpipe
      - gcp-steampipe-db:/home/<USER>/.steampipe/db
      - gcp-steampipe-plugins:/home/<USER>/.steampipe/plugins
      - ${HOME}/.config/gcloud:/home/<USER>/.config/gcloud
    working_dir: /app
    ports:
      - "9195:9194"
      - "9034:9033"
      - "9196:9193"
      - "8080:8080"
    environment:
      - STEAMPIPE_UPDATE_CHECK=false
      - POWERPIPE_UPDATE_CHECK=false
      - STEAMPIPE_DATABASE_PASSWORD=steampipe
      - STEAMPIPE_DATABASE_USER=steampipe
      - STEAMPIPE_DATABASE_NAME=steampipe
      - GOOGLE_APPLICATION_CREDENTIALS=/home/<USER>/.config/gcloud/application_default_credentials.json
      - STEAMPIPE_INIT_TIMEOUT=120
      - STEAMPIPE_TIMEOUT=600s
      - AUTH_TYPE=adc
      - STEAMPIPE_DATABASE_LISTEN_ADDRESS=0.0.0.0:9193
      - PROJECT_ID=vratant-test-prj
      - DOMAIN=microservice.neosecai.dev
    tty: true
    stdin_open: true
    networks:
      - app-network

  steampipe-azure:
    build:
      context: .
      dockerfile: custom/services/azure/Dockerfile
      args:
        - BASE_IMAGE=steampipe-base:latest
    depends_on:
      - steampipe-base
    container_name: steampipe-azure-compliance
    volumes:
      - ./custom/services/azure:/app/azure
      - ./custom/shared:/app/shared
      - ./opensource/mods/azure-compliance:/app/steampipe-mod-azure-compliance
      - azure-steampipe-internal:/home/<USER>/.steampipe/internal
      - ./custom/services/azure/config/azure.spc:/home/<USER>/.steampipe/config/azure.spc
      - azure-steampipe-logs:/home/<USER>/.steampipe/logs
      - azure-steampipe-config:/home/<USER>/.steampipe/config
      - azure-powerpipe-config:/home/<USER>/.powerpipe
      - azure-steampipe-db:/home/<USER>/.steampipe/db
      - azure-steampipe-plugins:/home/<USER>/.steampipe/plugins
      - ~/.azure:/home/<USER>/.azure
      - ${HOME}/.config/gcloud:/home/<USER>/.config/gcloud
    working_dir: /app
    ports:
      - "9197:9194"
      - "9035:9033"
      - "9198:9193"
      - "8083:8083"
    environment:
      - STEAMPIPE_UPDATE_CHECK=false
      - POWERPIPE_UPDATE_CHECK=false
      - STEAMPIPE_DATABASE_PASSWORD=steampipe
      - STEAMPIPE_DATABASE_USER=steampipe
      - STEAMPIPE_DATABASE_NAME=steampipe
      - STEAMPIPE_INIT_TIMEOUT=300
      - STEAMPIPE_TIMEOUT=600s
      - STEAMPIPE_DATABASE_TIMEOUT=300
      - DOMAIN=microservice.neosecai.dev
      - AUTH_TYPE=adc
      - CUSTOMER_ID=local
      - GOOGLE_APPLICATION_CREDENTIALS=/home/<USER>/.config/gcloud/application_default_credentials.json
    tty: true
    stdin_open: true
    networks:
      - app-network

  steampipe-aws:
    build:
      context: .
      dockerfile: custom/services/aws/Dockerfile
      args:
        - BASE_IMAGE=steampipe-base:latest
    depends_on:
      - steampipe-base
    container_name: steampipe-aws-compliance
    volumes:
      - ./custom/services/aws:/app/aws
      - ./custom/shared:/app/shared
      - ./opensource/mods/aws-compliance:/app/steampipe-mod-aws-compliance
      - aws-steampipe-internal:/home/<USER>/.steampipe/internal
      - ./custom/services/aws/config/aws.spc:/home/<USER>/.steampipe/config/aws.spc
      - aws-steampipe-logs:/home/<USER>/.steampipe/logs
      - aws-steampipe-config:/home/<USER>/.steampipe/config
      - aws-powerpipe-config:/home/<USER>/.powerpipe
      - aws-steampipe-db:/home/<USER>/.steampipe/db
      - aws-steampipe-plugins:/home/<USER>/.steampipe/plugins
      - ${HOME}/.aws:/home/<USER>/.aws
      - ${HOME}/.config/gcloud:/home/<USER>/.config/gcloud
    working_dir: /app
    ports:
      - "9194:9194"
      - "9033:9033"
      - "9193:9193"
      - "8082:8082"
    environment:
      - STEAMPIPE_UPDATE_CHECK=false
      - POWERPIPE_UPDATE_CHECK=false
      - STEAMPIPE_DATABASE_PASSWORD=steampipe
      - STEAMPIPE_DATABASE_USER=steampipe
      - STEAMPIPE_DATABASE_NAME=steampipe
      - AUTH_TYPE=adc
      - AWS_REGION=us-east-1
      - AWS_DEFAULT_REGION=us-east-1
      - GOOGLE_APPLICATION_CREDENTIALS=/home/<USER>/.config/gcloud/application_default_credentials.json
      - CUSTOMER_ID=local
      - DOMAIN=microservice.neosecai.dev
      - STEAMPIPE_INIT_TIMEOUT=300
      - STEAMPIPE_TIMEOUT=600s
      - STEAMPIPE_DATABASE_TIMEOUT=300
      - BOTO_CONFIG_DEBUG=1
    tty: true
    stdin_open: true
    networks:
      - app-network

  # API Gateway Service
  api-gateway:
    build:
      context: api-gateway/
      dockerfile: Dockerfile
    container_name: api-gateway
    ports:
      - "8000:8000"
    networks:
      - app-network

  # Admin UI Service
  admin-ui:
    build:
      context: custom/admin-ui/
      dockerfile: Dockerfile
    container_name: admin-ui
    ports:
      - "3001:3001"
    networks:
      - app-network

  # Dashboard UI Service (renamed from webapp)
  dashboard-ui:
    build:
      context: custom/dashboard/steampipe-secops-dashboard/frontend/
      dockerfile: Dockerfile
    container_name: dashboard-ui
    ports:
      - "8081:8081"
    environment:
      - BACKEND_GCP_URL=http://steampipe-gcp:8080
      - BACKEND_AWS_URL=http://steampipe-aws:8082
      - BACKEND_AZURE_URL=http://steampipe-azure:8083
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    volumes:
      - ./custom/dashboard/steampipe-secops-dashboard/frontend:/frontend
    networks:
      - app-network

networks:
  steampipe-network:
    driver: bridge